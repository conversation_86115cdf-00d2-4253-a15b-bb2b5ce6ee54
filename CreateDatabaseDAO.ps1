# Iraqi Private Institutes Accounting System - Database Creation using DAO
# PowerShell Script to Create Access Database

Write-Host "Starting database creation using DAO..." -ForegroundColor Green

try {
    # Create Access Application Object
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # Database path
    $dbPath = Join-Path (Get-Location) "InstituteAccounting.accdb"
    
    # Delete existing database if exists
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "Deleted existing database" -ForegroundColor Yellow
    }
    
    # Create new database
    $access.NewCurrentDatabase($dbPath)
    $db = $access.CurrentDb()
    
    Write-Host "Database created: $dbPath" -ForegroundColor Green
    
    # Create Institute Settings Table
    $tbl = $db.CreateTableDef("InstituteSettings")
    
    # Add fields to InstituteSettings
    $fld = $tbl.CreateField("SettingID", 4) # Long/AutoNumber
    $fld.Attributes = 16 # AutoIncrement
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("InstituteName", 10, 255) # Text
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("InstituteAddress", 10, 500)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PhoneNumber", 10, 50)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("InstituteSharePercentage", 6) # Single
    $fld.DefaultValue = 70
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("LogoPath", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CreatedDate", 8) # DateTime
    $fld.DefaultValue = "Now()"
    $tbl.Fields.Append($fld)
    
    # Create primary key
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("SettingID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created InstituteSettings table" -ForegroundColor Cyan
    
    # Create Courses Table
    $tbl = $db.CreateTableDef("Courses")
    
    $fld = $tbl.CreateField("CourseID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CourseName", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CourseDescription", 10, 500)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("DefaultFee", 5) # Currency
    $fld.DefaultValue = 0
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Duration", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("IsActive", 1) # Boolean
    $fld.DefaultValue = $true
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CreatedDate", 8)
    $fld.DefaultValue = "Now()"
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("CourseID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Courses table" -ForegroundColor Cyan
    
    # Create Teachers Table
    $tbl = $db.CreateTableDef("Teachers")
    
    $fld = $tbl.CreateField("TeacherID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("TeacherName", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Specialization", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PhoneNumber", 10, 50)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("SalaryType", 10, 50)
    $fld.DefaultValue = "'Percentage'"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("MonthlySalary", 5)
    $fld.DefaultValue = 0
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("SharePercentage", 6)
    $fld.DefaultValue = 30
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("HireDate", 8)
    $fld.DefaultValue = "Now()"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("IsActive", 1)
    $fld.DefaultValue = $true
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Notes", 12) # Memo
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("TeacherID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Teachers table" -ForegroundColor Cyan
    
    # Create Students Table
    $tbl = $db.CreateTableDef("Students")
    
    $fld = $tbl.CreateField("StudentID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("StudentName", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Gender", 10, 10)
    $fld.DefaultValue = "'Male'"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Age", 3) # Integer
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Grade", 10, 50)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CourseID", 4)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("TeacherID", 4)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("TotalFee", 5)
    $fld.DefaultValue = 0
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PaidAmount", 5)
    $fld.DefaultValue = 0
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("RemainingAmount", 5)
    $fld.DefaultValue = 0
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("RegistrationDate", 8)
    $fld.DefaultValue = "Now()"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("StudentStatus", 10, 50)
    $fld.DefaultValue = "'Active'"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("WithdrawalDate", 8)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("RefundAmount", 5)
    $fld.DefaultValue = 0
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ParentName", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ParentPhone", 10, 50)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Address", 10, 500)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Notes", 12)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("StudentID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Students table" -ForegroundColor Cyan
    
    # Create Payments Table
    $tbl = $db.CreateTableDef("Payments")
    
    $fld = $tbl.CreateField("PaymentID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("StudentID", 4)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("AmountPaid", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PaymentDate", 8)
    $fld.DefaultValue = "Now()"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PaymentMethod", 10, 50)
    $fld.DefaultValue = "'Cash'"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ReceiptNumber", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Notes", 12)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CreatedBy", 10, 100)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("PaymentID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Payments table" -ForegroundColor Cyan
    
    # Create Expenses Table
    $tbl = $db.CreateTableDef("Expenses")
    
    $fld = $tbl.CreateField("ExpenseID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ExpenseType", 10, 255)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Amount", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ExpenseDate", 8)
    $fld.DefaultValue = "Now()"
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Description", 10, 500)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ExpenseMonth", 3)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ExpenseYear", 3)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("IsRecurring", 1)
    $fld.DefaultValue = $false
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Category", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Notes", 12)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("ExpenseID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Expenses table" -ForegroundColor Cyan
    
    Write-Host "All tables created successfully!" -ForegroundColor Green
    
    # Save and close
    $access.DoCmd.Save()
    $access.Quit()
    
    Write-Host "Database created successfully!" -ForegroundColor Green
    Write-Host "File path: $dbPath" -ForegroundColor White
    
} catch {
    Write-Host "Error creating database: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception)" -ForegroundColor Red
} finally {
    # Clean up memory
    if ($access) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
