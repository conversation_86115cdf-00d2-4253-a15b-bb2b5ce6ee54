# Iraqi Private Institutes Accounting System - Simple Database Creation
# PowerShell Script to Create Access Database

Write-Host "Starting simple database creation..." -ForegroundColor Green

try {
    # Create Access Application Object
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # Database path
    $dbPath = Join-<PERSON> (Get-Location) "InstituteAccounting.accdb"
    
    # Delete existing database if exists
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "Deleted existing database" -ForegroundColor Yellow
    }
    
    # Create new database
    $access.NewCurrentDatabase($dbPath)
    $db = $access.CurrentDb()
    
    Write-Host "Database created: $dbPath" -ForegroundColor Green
    
    # Create Institute Settings Table
    $tbl = $db.CreateTableDef("InstituteSettings")
    
    # Add fields with proper sizes
    $fld = $tbl.CreateField("SettingID", 4) # Long/AutoNumber
    $fld.Attributes = 16 # AutoIncrement
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("InstituteName", 10, 100) # Text with smaller size
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("InstituteAddress", 10, 200)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PhoneNumber", 10, 20)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("InstituteSharePercentage", 6) # Single
    $tbl.Fields.Append($fld)
    
    # Create primary key
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("SettingID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created InstituteSettings table" -ForegroundColor Cyan
    
    # Create Courses Table
    $tbl = $db.CreateTableDef("Courses")
    
    $fld = $tbl.CreateField("CourseID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CourseName", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CourseDescription", 10, 200)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("DefaultFee", 5) # Currency
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Duration", 10, 50)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("IsActive", 1) # Boolean
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("CourseID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Courses table" -ForegroundColor Cyan
    
    # Create Teachers Table
    $tbl = $db.CreateTableDef("Teachers")
    
    $fld = $tbl.CreateField("TeacherID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("TeacherName", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Specialization", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PhoneNumber", 10, 20)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("SalaryType", 10, 20)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("MonthlySalary", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("SharePercentage", 6)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("IsActive", 1)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("TeacherID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Teachers table" -ForegroundColor Cyan
    
    # Create Students Table
    $tbl = $db.CreateTableDef("Students")
    
    $fld = $tbl.CreateField("StudentID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("StudentName", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Gender", 10, 10)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Age", 3) # Integer
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Grade", 10, 20)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("CourseID", 4)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("TeacherID", 4)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("TotalFee", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PaidAmount", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("RemainingAmount", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("RegistrationDate", 8)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("StudentStatus", 10, 20)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ParentName", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ParentPhone", 10, 20)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("StudentID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Students table" -ForegroundColor Cyan
    
    # Create Payments Table
    $tbl = $db.CreateTableDef("Payments")
    
    $fld = $tbl.CreateField("PaymentID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("StudentID", 4)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("AmountPaid", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PaymentDate", 8)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("PaymentMethod", 10, 20)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ReceiptNumber", 10, 50)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("PaymentID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Payments table" -ForegroundColor Cyan
    
    # Create Expenses Table
    $tbl = $db.CreateTableDef("Expenses")
    
    $fld = $tbl.CreateField("ExpenseID", 4)
    $fld.Attributes = 16
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ExpenseType", 10, 100)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Amount", 5)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("ExpenseDate", 8)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Description", 10, 200)
    $tbl.Fields.Append($fld)
    
    $fld = $tbl.CreateField("Category", 10, 50)
    $tbl.Fields.Append($fld)
    
    $idx = $tbl.CreateIndex("PrimaryKey")
    $idx.Primary = $true
    $idxFld = $idx.CreateField("ExpenseID")
    $idx.Fields.Append($idxFld)
    $tbl.Indexes.Append($idx)
    
    $db.TableDefs.Append($tbl)
    Write-Host "Created Expenses table" -ForegroundColor Cyan
    
    Write-Host "All tables created successfully!" -ForegroundColor Green
    
    # Insert some initial data
    Write-Host "Inserting initial data..." -ForegroundColor Yellow
    
    # Institute Settings
    $rs = $db.OpenRecordset("InstituteSettings")
    $rs.AddNew()
    $rs.Fields("InstituteName").Value = "معهد النور الأهلي"
    $rs.Fields("InstituteAddress").Value = "بغداد - الكرادة"
    $rs.Fields("PhoneNumber").Value = "07901234567"
    $rs.Fields("InstituteSharePercentage").Value = 70
    $rs.Update()
    $rs.Close()
    
    # Courses
    $rs = $db.OpenRecordset("Courses")
    $rs.AddNew()
    $rs.Fields("CourseName").Value = "دورة اللغة الإنجليزية - المستوى الأول"
    $rs.Fields("CourseDescription").Value = "دورة تأسيسية في اللغة الإنجليزية"
    $rs.Fields("DefaultFee").Value = 150000
    $rs.Fields("Duration").Value = "3 أشهر"
    $rs.Fields("IsActive").Value = $true
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("CourseName").Value = "دورة الرياضيات - الصف السادس"
    $rs.Fields("CourseDescription").Value = "دورة تقوية في الرياضيات"
    $rs.Fields("DefaultFee").Value = 120000
    $rs.Fields("Duration").Value = "شهرين"
    $rs.Fields("IsActive").Value = $true
    $rs.Update()
    $rs.Close()
    
    # Teachers
    $rs = $db.OpenRecordset("Teachers")
    $rs.AddNew()
    $rs.Fields("TeacherName").Value = "أ. أحمد محمد علي"
    $rs.Fields("Specialization").Value = "اللغة الإنجليزية"
    $rs.Fields("PhoneNumber").Value = "07701234567"
    $rs.Fields("SalaryType").Value = "نسبة"
    $rs.Fields("SharePercentage").Value = 30
    $rs.Fields("IsActive").Value = $true
    $rs.Update()
    
    $rs.AddNew()
    $rs.Fields("TeacherName").Value = "أ. فاطمة حسن"
    $rs.Fields("Specialization").Value = "الرياضيات"
    $rs.Fields("PhoneNumber").Value = "07801234567"
    $rs.Fields("SalaryType").Value = "نسبة"
    $rs.Fields("SharePercentage").Value = 35
    $rs.Fields("IsActive").Value = $true
    $rs.Update()
    $rs.Close()
    
    Write-Host "Initial data inserted successfully!" -ForegroundColor Green
    
    # Save and close
    $access.DoCmd.Save()
    $access.Quit()
    
    Write-Host "Database created successfully!" -ForegroundColor Green
    Write-Host "File path: $dbPath" -ForegroundColor White
    
} catch {
    Write-Host "Error creating database: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Clean up memory
    if ($access) {
        try {
            [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
        } catch {}
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
