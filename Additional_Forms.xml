<?xml version="1.0" encoding="UTF-8"?>
<!-- النماذج الإضافية - نظام محاسبة المعاهد الأهلية العراقية -->
<AdditionalForms>
    
    <!-- =============================================== -->
    <!-- نموذج دفع الأقساط -->
    <!-- =============================================== -->
    <PaymentForm name="نموذج_دفع_قسط">
        <Properties>
            <Caption>دفع قسط الطالب</Caption>
            <BackColor>#F8F9FA</BackColor>
            <Width>800</Width>
            <Height>600</Height>
            <RightToLeft>True</RightToLeft>
            <PopUp>True</PopUp>
            <Modal>True</Modal>
            <RecordSource>الأقساط_المدفوعة</RecordSource>
        </Properties>
        
        <Controls>
            <!-- عنوان النموذج -->
            <Label name="lbl_عنوان_دفع_قسط">
                <Properties>
                    <Left>300</Left>
                    <Top>20</Top>
                    <Width>200</Width>
                    <Height>40</Height>
                    <Caption>دفع قسط الطالب</Caption>
                    <FontSize>16</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <ForeColor>#2C3E50</ForeColor>
                </Properties>
            </Label>
            
            <!-- معلومات الطالب -->
            <Rectangle name="rect_معلومات_الطالب">
                <Properties>
                    <Left>50</Left>
                    <Top>80</Top>
                    <Width>700</Width>
                    <Height>150</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Sunken</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_اختيار_الطالب">
                <Properties>
                    <Left>650</Left>
                    <Top>100</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>اختر الطالب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_الطالب">
                <Properties>
                    <Left>300</Left>
                    <Top>100</Top>
                    <Width>320</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <RowSource>SELECT معرف_الطالب, اسم_الطالب FROM الطلاب WHERE حالة_الطالب = 'نشط' ORDER BY اسم_الطالب</RowSource>
                    <BoundColumn>1</BoundColumn>
                    <ColumnCount>2</ColumnCount>
                    <OnChange>[Event Procedure]</OnChange>
                </Properties>
            </ComboBox>
            
            <Label name="lbl_معلومات_الطالب">
                <Properties>
                    <Left>650</Left>
                    <Top>140</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>معلومات الطالب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_معلومات_الطالب">
                <Properties>
                    <Left>70</Left>
                    <Top>140</Top>
                    <Width>550</Width>
                    <Height>50</Height>
                    <FontSize>11</FontSize>
                    <Locked>True</Locked>
                    <BackColor>#F8F9FA</BackColor>
                    <Multiline>True</Multiline>
                </Properties>
            </Textbox>
            
            <!-- بيانات الدفع -->
            <Rectangle name="rect_بيانات_الدفع">
                <Properties>
                    <Left>50</Left>
                    <Top>250</Top>
                    <Width>700</Width>
                    <Height>200</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Sunken</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_المبلغ_المدفوع">
                <Properties>
                    <Left>650</Left>
                    <Top>270</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>المبلغ المدفوع:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_المبلغ_المدفوع">
                <Properties>
                    <Left>450</Left>
                    <Top>270</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>المبلغ_المدفوع</ControlSource>
                    <Format>Currency</Format>
                </Properties>
            </Textbox>
            
            <Label name="lbl_تاريخ_الدفع">
                <Properties>
                    <Left>350</Left>
                    <Top>270</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>تاريخ الدفع:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_تاريخ_الدفع">
                <Properties>
                    <Left>150</Left>
                    <Top>270</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>تاريخ_الدفع</ControlSource>
                    <Format>Short Date</Format>
                    <DefaultValue>Now()</DefaultValue>
                </Properties>
            </Textbox>
            
            <Label name="lbl_طريقة_الدفع">
                <Properties>
                    <Left>650</Left>
                    <Top>310</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>طريقة الدفع:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_طريقة_الدفع">
                <Properties>
                    <Left>450</Left>
                    <Top>310</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>طريقة_الدفع</ControlSource>
                    <RowSource>"نقد";"شيك";"تحويل مصرفي"</RowSource>
                    <DefaultValue>"نقد"</DefaultValue>
                </Properties>
            </ComboBox>
            
            <Label name="lbl_رقم_الإيصال">
                <Properties>
                    <Left>350</Left>
                    <Top>310</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>رقم الإيصال:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_رقم_الإيصال">
                <Properties>
                    <Left>150</Left>
                    <Top>310</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>رقم_الإيصال</ControlSource>
                </Properties>
            </Textbox>
            
            <Label name="lbl_ملاحظات_الدفع">
                <Properties>
                    <Left>650</Left>
                    <Top>350</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>ملاحظات:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_ملاحظات_الدفع">
                <Properties>
                    <Left>150</Left>
                    <Top>350</Top>
                    <Width>470</Width>
                    <Height>50</Height>
                    <FontSize>11</FontSize>
                    <ControlSource>الملاحظات</ControlSource>
                    <Multiline>True</Multiline>
                </Properties>
            </Textbox>
            
            <!-- أزرار العمليات -->
            <Button name="btn_حفظ_الدفع">
                <Properties>
                    <Left>550</Left>
                    <Top>480</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>حفظ الدفع</Caption>
                    <FontSize>12</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#27AE60</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>[Event Procedure]</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_طباعة_إيصال">
                <Properties>
                    <Left>420</Left>
                    <Top>480</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>طباعة إيصال</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#3498DB</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>[Event Procedure]</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_إلغاء_الدفع">
                <Properties>
                    <Left>290</Left>
                    <Top>480</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>إلغاء</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#E74C3C</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.Close</OnClick>
                </Properties>
            </Button>
        </Controls>
        
        <VBACode>
            <!-- كود VBA للنموذج -->
            <OnLoad>
                ' تحديد مصدر البيانات
                Me.RecordSource = "الأقساط_المدفوعة"
                DoCmd.GoToRecord , , acNewRec
            </OnLoad>
            
            <cmb_الطالب_Change>
                Dim معرف_الطالب As Long
                Dim معلومات As String
                
                If Not IsNull(Me.cmb_الطالب) Then
                    معرف_الطالب = Me.cmb_الطالب
                    معلومات = DLookup("اسم_الطالب & ' - ' & اسم_الدورة & ' - رسوم: ' & Format(رسوم_الدورة,'Currency') & ' - مدفوع: ' & Format(المبلغ_المدفوع,'Currency') & ' - متبقي: ' & Format(المبلغ_المتبقي,'Currency')", "الطلاب INNER JOIN الدورات ON الطلاب.معرف_الدورة = الدورات.معرف_الدورة", "معرف_الطالب = " & معرف_الطالب)
                    Me.txt_معلومات_الطالب = معلومات
                    Me.معرف_الطالب = معرف_الطالب
                End If
            </cmb_الطالب_Change>
            
            <btn_حفظ_الدفع_Click>
                ' التحقق من صحة البيانات
                If IsNull(Me.cmb_الطالب) Then
                    MsgBox "يرجى اختيار الطالب أولاً", vbExclamation
                    Exit Sub
                End If
                
                If IsNull(Me.txt_المبلغ_المدفوع) Or Me.txt_المبلغ_المدفوع <= 0 Then
                    MsgBox "يرجى إدخال مبلغ صحيح", vbExclamation
                    Exit Sub
                End If
                
                ' حفظ البيانات
                DoCmd.RunCommand acCmdSaveRecord
                
                ' تحديث المبلغ المدفوع في جدول الطلاب
                Call تحديث_المبلغ_المدفوع(Me.cmb_الطالب)
                
                MsgBox "تم حفظ الدفع بنجاح", vbInformation
                
                ' إغلاق النموذج
                DoCmd.Close
            </btn_حفظ_الدفع_Click>
            
            <btn_طباعة_إيصال_Click>
                ' طباعة إيصال الدفع
                DoCmd.OpenReport "تقرير_إيصال_الدفع", acViewPreview, , "معرف_الدفع = " & Me.معرف_الدفع
            </btn_طباعة_إيصال_Click>
        </VBACode>
    </PaymentForm>
    
    <!-- =============================================== -->
    <!-- نموذج انسحاب الطالب -->
    <!-- =============================================== -->
    <WithdrawalForm name="نموذج_انسحاب_الطالب">
        <Properties>
            <Caption>انسحاب الطالب</Caption>
            <BackColor>#F8F9FA</BackColor>
            <Width>800</Width>
            <Height>650</Height>
            <RightToLeft>True</RightToLeft>
            <PopUp>True</PopUp>
            <Modal>True</Modal>
        </Properties>
        
        <Controls>
            <!-- عنوان النموذج -->
            <Label name="lbl_عنوان_انسحاب">
                <Properties>
                    <Left>300</Left>
                    <Top>20</Top>
                    <Width>200</Width>
                    <Height>40</Height>
                    <Caption>انسحاب الطالب</Caption>
                    <FontSize>16</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <ForeColor>#E74C3C</ForeColor>
                </Properties>
            </Label>
            
            <!-- معلومات الطالب -->
            <Rectangle name="rect_معلومات_الطالب_انسحاب">
                <Properties>
                    <Left>50</Left>
                    <Top>80</Top>
                    <Width>700</Width>
                    <Height>150</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Sunken</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_اختيار_الطالب_انسحاب">
                <Properties>
                    <Left>650</Left>
                    <Top>100</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>اختر الطالب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_الطالب_انسحاب">
                <Properties>
                    <Left>300</Left>
                    <Top>100</Top>
                    <Width>320</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <RowSource>SELECT معرف_الطالب, اسم_الطالب FROM الطلاب WHERE حالة_الطالب = 'نشط' ORDER BY اسم_الطالب</RowSource>
                    <BoundColumn>1</BoundColumn>
                    <ColumnCount>2</ColumnCount>
                    <OnChange>[Event Procedure]</OnChange>
                </Properties>
            </ComboBox>
            
            <Label name="lbl_معلومات_الطالب_انسحاب">
                <Properties>
                    <Left>650</Left>
                    <Top>140</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>معلومات الطالب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_معلومات_الطالب_انسحاب">
                <Properties>
                    <Left>70</Left>
                    <Top>140</Top>
                    <Width>550</Width>
                    <Height>50</Height>
                    <FontSize>11</FontSize>
                    <Locked>True</Locked>
                    <BackColor>#F8F9FA</BackColor>
                    <Multiline>True</Multiline>
                </Properties>
            </Textbox>
            
            <!-- بيانات الانسحاب -->
            <Rectangle name="rect_بيانات_الانسحاب">
                <Properties>
                    <Left>50</Left>
                    <Top>250</Top>
                    <Width>700</Width>
                    <Height>250</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Sunken</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_تاريخ_الانسحاب">
                <Properties>
                    <Left>650</Left>
                    <Top>270</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>تاريخ الانسحاب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_تاريخ_الانسحاب">
                <Properties>
                    <Left>450</Left>
                    <Top>270</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <Format>Short Date</Format>
                    <DefaultValue>Now()</DefaultValue>
                </Properties>
            </Textbox>
            
            <Label name="lbl_سبب_الانسحاب">
                <Properties>
                    <Left>650</Left>
                    <Top>310</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>سبب الانسحاب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_سبب_الانسحاب">
                <Properties>
                    <Left>150</Left>
                    <Top>310</Top>
                    <Width>470</Width>
                    <Height>50</Height>
                    <FontSize>11</FontSize>
                    <Multiline>True</Multiline>
                </Properties>
            </Textbox>
            
            <Label name="lbl_نسبة_الخصم_انسحاب">
                <Properties>
                    <Left>650</Left>
                    <Top>380</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>نسبة الخصم (%):</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_نسبة_الخصم_انسحاب">
                <Properties>
                    <Left>450</Left>
                    <Top>380</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <Format>Percent</Format>
                    <DefaultValue>0.1</DefaultValue>
                    <OnChange>[Event Procedure]</OnChange>
                </Properties>
            </Textbox>
            
            <Label name="lbl_المبلغ_المسترد">
                <Properties>
                    <Left>300</Left>
                    <Top>380</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>المبلغ المسترد:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_المبلغ_المسترد">
                <Properties>
                    <Left>100</Left>
                    <Top>380</Top>
                    <Width>170</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <Format>Currency</Format>
                    <Locked>True</Locked>
                    <BackColor>#F8F9FA</BackColor>
                </Properties>
            </Textbox>
            
            <Label name="lbl_ملاحظات_الانسحاب">
                <Properties>
                    <Left>650</Left>
                    <Top>420</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>ملاحظات:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_ملاحظات_الانسحاب">
                <Properties>
                    <Left>150</Left>
                    <Top>420</Top>
                    <Width>470</Width>
                    <Height>50</Height>
                    <FontSize>11</FontSize>
                    <Multiline>True</Multiline>
                </Properties>
            </Textbox>
            
            <!-- أزرار العمليات -->
            <Button name="btn_تأكيد_الانسحاب">
                <Properties>
                    <Left>550</Left>
                    <Top>530</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>تأكيد الانسحاب</Caption>
                    <FontSize>12</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#E74C3C</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>[Event Procedure]</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_إلغاء_الانسحاب">
                <Properties>
                    <Left>420</Left>
                    <Top>530</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>إلغاء</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#95A5A6</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.Close</OnClick>
                </Properties>
            </Button>
        </Controls>
        
        <VBACode>
            <!-- كود VBA للنموذج -->
            <cmb_الطالب_انسحاب_Change>
                Dim معرف_الطالب As Long
                Dim معلومات As String
                
                If Not IsNull(Me.cmb_الطالب_انسحاب) Then
                    معرف_الطالب = Me.cmb_الطالب_انسحاب
                    معلومات = DLookup("اسم_الطالب & ' - ' & اسم_الدورة & ' - رسوم: ' & Format(رسوم_الدورة,'Currency') & ' - مدفوع: ' & Format(المبلغ_المدفوع,'Currency') & ' - متبقي: ' & Format(المبلغ_المتبقي,'Currency')", "الطلاب INNER JOIN الدورات ON الطلاب.معرف_الدورة = الدورات.معرف_الدورة", "معرف_الطالب = " & معرف_الطالب)
                    Me.txt_معلومات_الطالب_انسحاب = معلومات
                    Call حساب_المبلغ_المسترد
                End If
            </cmb_الطالب_انسحاب_Change>
            
            <txt_نسبة_الخصم_انسحاب_Change>
                Call حساب_المبلغ_المسترد
            </txt_نسبة_الخصم_انسحاب_Change>
            
            <حساب_المبلغ_المسترد>
                Dim المبلغ_المدفوع As Currency
                Dim نسبة_الخصم As Double
                Dim المبلغ_المسترد As Currency
                
                If Not IsNull(Me.cmb_الطالب_انسحاب) And Not IsNull(Me.txt_نسبة_الخصم_انسحاب) Then
                    المبلغ_المدفوع = Nz(DLookup("المبلغ_المدفوع", "الطلاب", "معرف_الطالب = " & Me.cmb_الطالب_انسحاب), 0)
                    نسبة_الخصم = Nz(Me.txt_نسبة_الخصم_انسحاب, 0)
                    المبلغ_المسترد = المبلغ_المدفوع * (1 - نسبة_الخصم)
                    Me.txt_المبلغ_المسترد = المبلغ_المسترد
                End If
            </حساب_المبلغ_المسترد>
            
            <btn_تأكيد_الانسحاب_Click>
                ' التحقق من صحة البيانات
                If IsNull(Me.cmb_الطالب_انسحاب) Then
                    MsgBox "يرجى اختيار الطالب أولاً", vbExclamation
                    Exit Sub
                End If
                
                If IsNull(Me.txt_سبب_الانسحاب) Or Trim(Me.txt_سبب_الانسحاب) = "" Then
                    MsgBox "يرجى إدخال سبب الانسحاب", vbExclamation
                    Exit Sub
                End If
                
                ' تأكيد الانسحاب
                If MsgBox("هل أنت متأكد من انسحاب الطالب؟" & vbCrLf & "هذا الإجراء لا يمكن التراجع عنه", vbYesNo + vbQuestion, "تأكيد الانسحاب") = vbYes Then
                    Dim المبلغ_المسترد As Currency
                    المبلغ_المسترد = إنسحاب_الطالب(Me.cmb_الطالب_انسحاب, Me.txt_سبب_الانسحاب, Me.txt_نسبة_الخصم_انسحاب)
                    
                    MsgBox "تم انسحاب الطالب بنجاح" & vbCrLf & "المبلغ المسترد: " & Format(المبلغ_المسترد, "Currency"), vbInformation
                    
                    ' إغلاق النموذج
                    DoCmd.Close
                End If
            </btn_تأكيد_الانسحاب_Click>
        </VBACode>
    </WithdrawalForm>
    
    <!-- =============================================== -->
    <!-- نموذج إدارة المدرسين -->
    <!-- =============================================== -->
    <TeachersForm name="نموذج_المدرسين">
        <Properties>
            <Caption>إدارة المدرسين</Caption>
            <BackColor>#F8F9FA</BackColor>
            <Width>1400</Width>
            <Height>900</Height>
            <RightToLeft>True</RightToLeft>
            <RecordSource>المدرسين</RecordSource>
        </Properties>
        
        <Controls>
            <!-- عنوان النموذج -->
            <Label name="lbl_عنوان_المدرسين">
                <Properties>
                    <Left>600</Left>
                    <Top>20</Top>
                    <Width>200</Width>
                    <Height>40</Height>
                    <Caption>إدارة المدرسين</Caption>
                    <FontSize>18</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <ForeColor>#2C3E50</ForeColor>
                </Properties>
            </Label>
            
            <!-- بيانات المدرس -->
            <Rectangle name="rect_بيانات_المدرس">
                <Properties>
                    <Left>50</Left>
                    <Top>80</Top>
                    <Width>1300</Width>
                    <Height>300</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Sunken</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <!-- الصف الأول -->
            <Label name="lbl_اسم_المدرس">
                <Properties>
                    <Left>1200</Left>
                    <Top>100</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>اسم المدرس:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_اسم_المدرس">
                <Properties>
                    <Left>900</Left>
                    <Top>100</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>اسم_المدرس</ControlSource>
                </Properties>
            </Textbox>
            
            <Label name="lbl_التخصص">
                <Properties>
                    <Left>800</Left>
                    <Top>100</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>التخصص:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_التخصص">
                <Properties>
                    <Left>500</Left>
                    <Top>100</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>التخصص</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- الصف الثاني -->
            <Label name="lbl_رقم_هاتف_المدرس">
                <Properties>
                    <Left>1200</Left>
                    <Top>140</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>رقم الهاتف:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_رقم_هاتف_المدرس">
                <Properties>
                    <Left>900</Left>
                    <Top>140</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>رقم_الهاتف</ControlSource>
                </Properties>
            </Textbox>
            
            <Label name="lbl_عنوان_المدرس">
                <Properties>
                    <Left>800</Left>
                    <Top>140</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>العنوان:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_عنوان_المدرس">
                <Properties>
                    <Left>300</Left>
                    <Top>140</Top>
                    <Width>480</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>العنوان</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- الصف الثالث - نوع الراتب -->
            <Label name="lbl_نوع_الراتب">
                <Properties>
                    <Left>1200</Left>
                    <Top>180</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>نوع الراتب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_نوع_الراتب">
                <Properties>
                    <Left>900</Left>
                    <Top>180</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>نوع_الراتب</ControlSource>
                    <RowSource>"نسبة";"ثابت";"مختلط"</RowSource>
                    <DefaultValue>"نسبة"</DefaultValue>
                </Properties>
            </ComboBox>
            
            <Label name="lbl_الراتب_الثابت">
                <Properties>
                    <Left>800</Left>
                    <Top>180</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>الراتب الثابت:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_الراتب_الثابت">
                <Properties>
                    <Left>600</Left>
                    <Top>180</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>الراتب_الثابت</ControlSource>
                    <Format>Currency</Format>
                    <DefaultValue>0</DefaultValue>
                </Properties>
            </Textbox>
            
            <Label name="lbl_نسبة_المدرس">
                <Properties>
                    <Left>500</Left>
                    <Top>180</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>نسبة المدرس (%):</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_نسبة_المدرس">
                <Properties>
                    <Left>350</Left>
                    <Top>180</Top>
                    <Width>130</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>نسبة_المدرس</ControlSource>
                    <Format>Percent</Format>
                    <DefaultValue>0.3</DefaultValue>
                </Properties>
            </Textbox>
            
            <!-- الصف الرابع -->
            <Label name="lbl_تاريخ_التعيين">
                <Properties>
                    <Left>1200</Left>
                    <Top>220</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>تاريخ التعيين:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_تاريخ_التعيين">
                <Properties>
                    <Left>900</Left>
                    <Top>220</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>تاريخ_التعيين</ControlSource>
                    <Format>Short Date</Format>
                    <DefaultValue>Now()</DefaultValue>
                </Properties>
            </Textbox>
            
            <Label name="lbl_حالة_المدرس">
                <Properties>
                    <Left>800</Left>
                    <Top>220</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>الحالة:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_حالة_المدرس">
                <Properties>
                    <Left>600</Left>
                    <Top>220</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>حالة_المدرس</ControlSource>
                    <RowSource>"نشط";"متوقف";"مطرود"</RowSource>
                    <DefaultValue>"نشط"</DefaultValue>
                </Properties>
            </ComboBox>
            
            <!-- الملاحظات -->
            <Label name="lbl_ملاحظات_المدرس">
                <Properties>
                    <Left>1200</Left>
                    <Top>260</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>ملاحظات:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_ملاحظات_المدرس">
                <Properties>
                    <Left>350</Left>
                    <Top>260</Top>
                    <Width>830</Width>
                    <Height>50</Height>
                    <FontSize>11</FontSize>
                    <ControlSource>ملاحظات</ControlSource>
                    <Multiline>True</Multiline>
                </Properties>
            </Textbox>
            
            <!-- أزرار العمليات -->
            <Button name="btn_مدرس_جديد">
                <Properties>
                    <Left>1200</Left>
                    <Top>330</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>مدرس جديد</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#27AE60</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.GoToRecord , , acNewRec</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_حفظ_المدرس">
                <Properties>
                    <Left>1070</Left>
                    <Top>330</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>حفظ البيانات</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#3498DB</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.RunCommand acCmdSaveRecord</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_حذف_المدرس">
                <Properties>
                    <Left>940</Left>
                    <Top>330</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>حذف المدرس</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#E74C3C</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.RunCommand acCmdDeleteRecord</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_حساب_الراتب">
                <Properties>
                    <Left>810</Left>
                    <Top>330</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>حساب الراتب</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#8E44AD</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.OpenForm "نموذج_حساب_راتب_المدرس"</OnClick>
                </Properties>
            </Button>
            
            <!-- قائمة المدرسين -->
            <SubForm name="subform_قائمة_المدرسين">
                <Properties>
                    <Left>50</Left>
                    <Top>400</Top>
                    <Width>1300</Width>
                    <Height>400</Height>
                    <SourceObject>Query.استعلام_المدرسين</SourceObject>
                    <LinkChildFields>معرف_المدرس</LinkChildFields>
                    <LinkMasterFields>معرف_المدرس</LinkMasterFields>
                </Properties>
            </SubForm>
            
            <!-- زر العودة -->
            <Button name="btn_العودة_مدرسين">
                <Properties>
                    <Left>100</Left>
                    <Top>820</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>العودة للرئيسية</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#95A5A6</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.OpenForm "الواجهة_الرئيسية"</OnClick>
                </Properties>
            </Button>
        </Controls>
    </TeachersForm>
</AdditionalForms>