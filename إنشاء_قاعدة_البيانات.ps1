# سكريبت PowerShell لإنشاء قاعدة بيانات Access
# Iraqi Private Institutes Accounting System - Database Creation

Write-Host "بدء إنشاء قاعدة البيانات..." -ForegroundColor Green

try {
    # إنشاء كائن Access
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # مسار قاعدة البيانات
    $dbPath = Join-Path (Get-Location) "نظام_محاسبة_المعاهد.accdb"
    
    # حذف قاعدة البيانات إذا كانت موجودة
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "تم حذف قاعدة البيانات القديمة" -ForegroundColor Yellow
    }
    
    # إنشاء قاعدة بيانات جديدة
    $access.NewCurrentDatabase($dbPath)
    $db = $access.CurrentDb()
    
    Write-Host "تم إنشاء قاعدة البيانات: $dbPath" -ForegroundColor Green
    
    # إنشاء الجداول باستخدام SQL
    
    # جدول إعدادات المعهد
    $sql = @"
CREATE TABLE إعدادات_المعهد (
    رقم_الإعداد AUTOINCREMENT PRIMARY KEY,
    اسم_المعهد TEXT(255) NOT NULL,
    عنوان_المعهد TEXT(500),
    رقم_الهاتف TEXT(50),
    نسبة_المعهد SINGLE DEFAULT 70,
    مسار_الشعار TEXT(255),
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    تاريخ_التعديل DATETIME DEFAULT Now()
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول إعدادات المعهد" -ForegroundColor Cyan
    
    # جدول الدورات
    $sql = @"
CREATE TABLE الدورات (
    رقم_الدورة AUTOINCREMENT PRIMARY KEY,
    اسم_الدورة TEXT(255) NOT NULL,
    وصف_الدورة TEXT(500),
    الرسوم_الافتراضية CURRENCY DEFAULT 0,
    المدة TEXT(100),
    نشطة YESNO DEFAULT True,
    تاريخ_الإنشاء DATETIME DEFAULT Now()
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول الدورات" -ForegroundColor Cyan
    
    # جدول المدرسين
    $sql = @"
CREATE TABLE المدرسين (
    رقم_المدرس AUTOINCREMENT PRIMARY KEY,
    اسم_المدرس TEXT(255) NOT NULL,
    التخصص TEXT(255),
    رقم_الهاتف TEXT(50),
    نوع_الأجر TEXT(50) DEFAULT 'نسبة',
    الأجر_الشهري CURRENCY DEFAULT 0,
    نسبة_المشاركة SINGLE DEFAULT 30,
    تاريخ_التوظيف DATETIME DEFAULT Now(),
    نشط YESNO DEFAULT True,
    ملاحظات MEMO
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول المدرسين" -ForegroundColor Cyan
    
    # جدول الطلاب
    $sql = @"
CREATE TABLE الطلاب (
    رقم_الطالب AUTOINCREMENT PRIMARY KEY,
    اسم_الطالب TEXT(255) NOT NULL,
    الجنس TEXT(10) DEFAULT 'ذكر',
    العمر INTEGER,
    الصف TEXT(50),
    رقم_الدورة INTEGER,
    رقم_المدرس INTEGER,
    القسط_الكلي CURRENCY DEFAULT 0,
    المبلغ_المدفوع CURRENCY DEFAULT 0,
    المبلغ_المتبقي CURRENCY DEFAULT 0,
    تاريخ_التسجيل DATETIME DEFAULT Now(),
    حالة_الطالب TEXT(50) DEFAULT 'نشط',
    تاريخ_الانسحاب DATETIME,
    المبلغ_المسترجع CURRENCY DEFAULT 0,
    اسم_ولي_الأمر TEXT(255),
    هاتف_ولي_الأمر TEXT(50),
    العنوان TEXT(500),
    ملاحظات MEMO
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول الطلاب" -ForegroundColor Cyan
    
    # جدول الدفعات
    $sql = @"
CREATE TABLE الدفعات (
    رقم_الدفعة AUTOINCREMENT PRIMARY KEY,
    رقم_الطالب INTEGER NOT NULL,
    المبلغ_المدفوع CURRENCY NOT NULL,
    تاريخ_الدفع DATETIME DEFAULT Now(),
    طريقة_الدفع TEXT(50) DEFAULT 'نقدي',
    رقم_الإيصال TEXT(100),
    ملاحظات MEMO,
    المستخدم TEXT(100)
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول الدفعات" -ForegroundColor Cyan
    
    # جدول المصاريف
    $sql = @"
CREATE TABLE المصاريف (
    رقم_المصروف AUTOINCREMENT PRIMARY KEY,
    نوع_المصروف TEXT(255) NOT NULL,
    المبلغ CURRENCY NOT NULL,
    تاريخ_المصروف DATETIME DEFAULT Now(),
    الوصف TEXT(500),
    الشهر INTEGER,
    السنة INTEGER,
    مصروف_دوري YESNO DEFAULT False,
    الفئة TEXT(100),
    ملاحظات MEMO
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول المصاريف" -ForegroundColor Cyan
    
    # جدول أرباح المدرسين
    $sql = @"
CREATE TABLE أرباح_المدرسين (
    رقم_الربح AUTOINCREMENT PRIMARY KEY,
    رقم_المدرس INTEGER NOT NULL,
    الشهر INTEGER NOT NULL,
    السنة INTEGER NOT NULL,
    عدد_الطلاب INTEGER DEFAULT 0,
    مجموع_أقساط_الطلاب CURRENCY DEFAULT 0,
    نصيب_المدرس CURRENCY DEFAULT 0,
    نصيب_المعهد CURRENCY DEFAULT 0,
    مدفوع YESNO DEFAULT False,
    تاريخ_الدفع DATETIME,
    ملاحظات MEMO
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول أرباح المدرسين" -ForegroundColor Cyan
    
    # جدول الانسحابات
    $sql = @"
CREATE TABLE الانسحابات (
    رقم_الانسحاب AUTOINCREMENT PRIMARY KEY,
    رقم_الطالب INTEGER NOT NULL,
    تاريخ_الانسحاب DATETIME DEFAULT Now(),
    السبب TEXT(500),
    المبلغ_المسترجع CURRENCY DEFAULT 0,
    نسبة_الاسترجاع SINGLE DEFAULT 0,
    المعالج TEXT(100),
    ملاحظات MEMO
)
"@
    $db.Execute($sql)
    Write-Host "تم إنشاء جدول الانسحابات" -ForegroundColor Cyan
    
    # إدراج البيانات الأولية
    Write-Host "إدراج البيانات الأولية..." -ForegroundColor Yellow
    
    # إعدادات المعهد
    $sql = "INSERT INTO إعدادات_المعهد (اسم_المعهد, عنوان_المعهد, رقم_الهاتف, نسبة_المعهد) VALUES ('معهد النور الأهلي', 'بغداد - الكرادة', '07901234567', 70)"
    $db.Execute($sql)
    
    # الدورات
    $sql = "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة) VALUES ('دورة اللغة الإنجليزية - المستوى الأول', 'دورة تأسيسية في اللغة الإنجليزية', 150000, '3 أشهر')"
    $db.Execute($sql)
    
    $sql = "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة) VALUES ('دورة اللغة الإنجليزية - المستوى الثاني', 'دورة متوسطة في اللغة الإنجليزية', 200000, '3 أشهر')"
    $db.Execute($sql)
    
    $sql = "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة) VALUES ('دورة الرياضيات - الصف السادس', 'دورة تقوية في الرياضيات للصف السادس', 120000, 'شهرين')"
    $db.Execute($sql)
    
    $sql = "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة) VALUES ('دورة الفيزياء - الصف الثالث متوسط', 'دورة تقوية في الفيزياء', 180000, '3 أشهر')"
    $db.Execute($sql)
    
    # المدرسين
    $sql = "INSERT INTO المدرسين (اسم_المدرس, التخصص, رقم_الهاتف, نوع_الأجر, نسبة_المشاركة) VALUES ('أ. أحمد محمد علي', 'اللغة الإنجليزية', '07701234567', 'نسبة', 30)"
    $db.Execute($sql)
    
    $sql = "INSERT INTO المدرسين (اسم_المدرس, التخصص, رقم_الهاتف, نوع_الأجر, نسبة_المشاركة) VALUES ('أ. فاطمة حسن', 'الرياضيات', '07801234567', 'نسبة', 35)"
    $db.Execute($sql)
    
    $sql = "INSERT INTO المدرسين (اسم_المدرس, التخصص, رقم_الهاتف, نوع_الأجر, الأجر_الشهري) VALUES ('د. علي جاسم', 'الفيزياء', '07901234567', 'ثابت', 500000)"
    $db.Execute($sql)
    
    Write-Host "تم إدراج البيانات الأولية بنجاح" -ForegroundColor Green
    
    # حفظ وإغلاق
    $access.DoCmd.Save()
    $access.Quit()
    
    Write-Host "تم إنشاء قاعدة البيانات بنجاح!" -ForegroundColor Green
    Write-Host "مسار الملف: $dbPath" -ForegroundColor White
    
} catch {
    Write-Host "خطأ في إنشاء قاعدة البيانات: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # تنظيف الذاكرة
    if ($access) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
