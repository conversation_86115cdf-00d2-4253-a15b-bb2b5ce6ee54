# Iraqi Private Institutes Accounting System - Database Creation
# PowerShell Script to Create Access Database

Write-Host "Starting database creation..." -ForegroundColor Green

try {
    # Create Access Application Object
    $access = New-Object -ComObject Access.Application
    $access.Visible = $false
    
    # Database path
    $dbPath = Join-<PERSON> (Get-Location) "InstituteAccounting.accdb"
    
    # Delete existing database if exists
    if (Test-Path $dbPath) {
        Remove-Item $dbPath -Force
        Write-Host "Deleted existing database" -ForegroundColor Yellow
    }
    
    # Create new database
    $access.NewCurrentDatabase($dbPath)
    $db = $access.CurrentDb()
    
    Write-Host "Database created: $dbPath" -ForegroundColor Green
    
    # Create tables using SQL
    
    # Institute Settings Table
    $sql = @"
CREATE TABLE InstituteSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    InstituteName TEXT(255) NOT NULL,
    InstituteAddress TEXT(500),
    PhoneNumber TEXT(50),
    InstituteSharePercentage SINGLE DEFAULT 70,
    LogoPath TEXT(255),
    CreatedDate DATETIME DEFAULT Now(),
    ModifiedDate DATETIME DEFAULT Now()
)
"@
    $db.Execute($sql)
    Write-Host "Created InstituteSettings table" -ForegroundColor Cyan
    
    # Courses Table
    $sql = @"
CREATE TABLE Courses (
    CourseID AUTOINCREMENT PRIMARY KEY,
    CourseName TEXT(255) NOT NULL,
    CourseDescription TEXT(500),
    DefaultFee CURRENCY DEFAULT 0,
    Duration TEXT(100),
    IsActive YESNO DEFAULT True,
    CreatedDate DATETIME DEFAULT Now()
)
"@
    $db.Execute($sql)
    Write-Host "Created Courses table" -ForegroundColor Cyan
    
    # Teachers Table
    $sql = @"
CREATE TABLE Teachers (
    TeacherID AUTOINCREMENT PRIMARY KEY,
    TeacherName TEXT(255) NOT NULL,
    Specialization TEXT(255),
    PhoneNumber TEXT(50),
    SalaryType TEXT(50) DEFAULT 'Percentage',
    MonthlySalary CURRENCY DEFAULT 0,
    SharePercentage SINGLE DEFAULT 30,
    HireDate DATETIME DEFAULT Now(),
    IsActive YESNO DEFAULT True,
    Notes MEMO
)
"@
    $db.Execute($sql)
    Write-Host "Created Teachers table" -ForegroundColor Cyan
    
    # Students Table
    $sql = @"
CREATE TABLE Students (
    StudentID AUTOINCREMENT PRIMARY KEY,
    StudentName TEXT(255) NOT NULL,
    Gender TEXT(10) DEFAULT 'Male',
    Age INTEGER,
    Grade TEXT(50),
    CourseID INTEGER,
    TeacherID INTEGER,
    TotalFee CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    RemainingAmount CURRENCY DEFAULT 0,
    RegistrationDate DATETIME DEFAULT Now(),
    StudentStatus TEXT(50) DEFAULT 'Active',
    WithdrawalDate DATETIME,
    RefundAmount CURRENCY DEFAULT 0,
    ParentName TEXT(255),
    ParentPhone TEXT(50),
    Address TEXT(500),
    Notes MEMO
)
"@
    $db.Execute($sql)
    Write-Host "Created Students table" -ForegroundColor Cyan
    
    # Payments Table
    $sql = @"
CREATE TABLE Payments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    StudentID INTEGER NOT NULL,
    AmountPaid CURRENCY NOT NULL,
    PaymentDate DATETIME DEFAULT Now(),
    PaymentMethod TEXT(50) DEFAULT 'Cash',
    ReceiptNumber TEXT(100),
    Notes MEMO,
    CreatedBy TEXT(100)
)
"@
    $db.Execute($sql)
    Write-Host "Created Payments table" -ForegroundColor Cyan
    
    # Expenses Table
    $sql = @"
CREATE TABLE Expenses (
    ExpenseID AUTOINCREMENT PRIMARY KEY,
    ExpenseType TEXT(255) NOT NULL,
    Amount CURRENCY NOT NULL,
    ExpenseDate DATETIME DEFAULT Now(),
    Description TEXT(500),
    ExpenseMonth INTEGER,
    ExpenseYear INTEGER,
    IsRecurring YESNO DEFAULT False,
    Category TEXT(100),
    Notes MEMO
)
"@
    $db.Execute($sql)
    Write-Host "Created Expenses table" -ForegroundColor Cyan
    
    # Teacher Earnings Table
    $sql = @"
CREATE TABLE TeacherEarnings (
    EarningID AUTOINCREMENT PRIMARY KEY,
    TeacherID INTEGER NOT NULL,
    Month INTEGER NOT NULL,
    Year INTEGER NOT NULL,
    StudentCount INTEGER DEFAULT 0,
    TotalStudentFees CURRENCY DEFAULT 0,
    TeacherShare CURRENCY DEFAULT 0,
    InstituteShare CURRENCY DEFAULT 0,
    IsPaid YESNO DEFAULT False,
    PaymentDate DATETIME,
    Notes MEMO
)
"@
    $db.Execute($sql)
    Write-Host "Created TeacherEarnings table" -ForegroundColor Cyan
    
    # Withdrawals Table
    $sql = @"
CREATE TABLE Withdrawals (
    WithdrawalID AUTOINCREMENT PRIMARY KEY,
    StudentID INTEGER NOT NULL,
    WithdrawalDate DATETIME DEFAULT Now(),
    Reason TEXT(500),
    RefundAmount CURRENCY DEFAULT 0,
    RefundPercentage SINGLE DEFAULT 0,
    ProcessedBy TEXT(100),
    Notes MEMO
)
"@
    $db.Execute($sql)
    Write-Host "Created Withdrawals table" -ForegroundColor Cyan
    
    # Insert initial data
    Write-Host "Inserting initial data..." -ForegroundColor Yellow
    
    # Institute Settings
    $sql = "INSERT INTO InstituteSettings (InstituteName, InstituteAddress, PhoneNumber, InstituteSharePercentage) VALUES ('Al-Noor Private Institute', 'Baghdad - Karrada', '07901234567', 70)"
    $db.Execute($sql)
    
    # Courses
    $sql = "INSERT INTO Courses (CourseName, CourseDescription, DefaultFee, Duration) VALUES ('English Language - Level 1', 'Basic English Language Course', 150000, '3 months')"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Courses (CourseName, CourseDescription, DefaultFee, Duration) VALUES ('English Language - Level 2', 'Intermediate English Language Course', 200000, '3 months')"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Courses (CourseName, CourseDescription, DefaultFee, Duration) VALUES ('Mathematics - Grade 6', 'Mathematics Reinforcement Course for Grade 6', 120000, '2 months')"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Courses (CourseName, CourseDescription, DefaultFee, Duration) VALUES ('Physics - Grade 9', 'Physics Reinforcement Course', 180000, '3 months')"
    $db.Execute($sql)
    
    # Teachers
    $sql = "INSERT INTO Teachers (TeacherName, Specialization, PhoneNumber, SalaryType, SharePercentage) VALUES ('Ahmed Mohammed Ali', 'English Language', '07701234567', 'Percentage', 30)"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Teachers (TeacherName, Specialization, PhoneNumber, SalaryType, SharePercentage) VALUES ('Fatima Hassan', 'Mathematics', '07801234567', 'Percentage', 35)"
    $db.Execute($sql)
    
    $sql = "INSERT INTO Teachers (TeacherName, Specialization, PhoneNumber, SalaryType, MonthlySalary) VALUES ('Dr. Ali Jasim', 'Physics', '07901234567', 'Fixed', 500000)"
    $db.Execute($sql)
    
    Write-Host "Initial data inserted successfully" -ForegroundColor Green
    
    # Save and close
    $access.DoCmd.Save()
    $access.Quit()
    
    Write-Host "Database created successfully!" -ForegroundColor Green
    Write-Host "File path: $dbPath" -ForegroundColor White
    
} catch {
    Write-Host "Error creating database: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    # Clean up memory
    if ($access) {
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($access) | Out-Null
    }
    [System.GC]::Collect()
    [System.GC]::WaitForPendingFinalizers()
}
