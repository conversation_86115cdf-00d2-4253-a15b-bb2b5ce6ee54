' ===========================================
' نظام محاسبة المعاهد الأهلية العراقية
' Advanced VBA Macros and Procedures
' ===========================================

Option Compare Database
Option Explicit

' ===========================================
' Module: العمليات_المتقدمة
' ===========================================

' دالة لإنشاء نسخة احتياطية من قاعدة البيانات
Public Function إنشاء_نسخة_احتياطية() As Boolean
    On Error GoTo ErrorHandler
    
    Dim مسار_النسخة_الاحتياطية As String
    Dim اسم_الملف As String
    Dim مسار_قاعدة_البيانات As String
    
    ' إنشاء اسم الملف مع التاريخ والوقت
    اسم_الملف = "نظام_محاسبة_المعهد_نسخة_احتياطية_" & Format(Now(), "yyyy-mm-dd_hh-nn-ss") & ".accdb"
    
    ' تحديد مسار النسخة الاحتياطية
    مسار_النسخة_الاحتياطية = CurrentProject.Path & "\النسخ_الاحتياطية\"
    
    ' إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
    If Dir(مسار_النسخة_الاحتياطية, vbDirectory) = "" Then
        MkDir مسار_النسخة_الاحتياطية
    End If
    
    ' نسخ قاعدة البيانات
    مسار_قاعدة_البيانات = CurrentProject.FullName
    FileCopy مسار_قاعدة_البيانات, مسار_النسخة_الاحتياطية & اسم_الملف
    
    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح في:" & vbCrLf & مسار_النسخة_الاحتياطية & اسم_الملف, vbInformation, "نسخة احتياطية"
    
    إنشاء_نسخة_احتياطية = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء النسخة الاحتياطية:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    إنشاء_نسخة_احتياطية = False
End Function

' دالة لتصدير البيانات إلى Excel
Public Function تصدير_البيانات_إلى_Excel(نوع_البيانات As String) As Boolean
    On Error GoTo ErrorHandler
    
    Dim مسار_التصدير As String
    Dim اسم_الملف As String
    Dim اسم_الجدول As String
    
    ' تحديد الجدول حسب نوع البيانات
    Select Case نوع_البيانات
        Case "الطلاب"
            اسم_الجدول = "استعلام_الطلاب"
            اسم_الملف = "بيانات_الطلاب_" & Format(Now(), "yyyy-mm-dd") & ".xlsx"
        Case "المدرسين"
            اسم_الجدول = "استعلام_المدرسين"
            اسم_الملف = "بيانات_المدرسين_" & Format(Now(), "yyyy-mm-dd") & ".xlsx"
        Case "الأقساط"
            اسم_الجدول = "استعلام_الأقساط_المدفوعة_الشهرية"
            اسم_الملف = "بيانات_الأقساط_" & Format(Now(), "yyyy-mm-dd") & ".xlsx"
        Case "المصاريف"
            اسم_الجدول = "استعلام_المصاريف_الشهرية"
            اسم_الملف = "بيانات_المصاريف_" & Format(Now(), "yyyy-mm-dd") & ".xlsx"
        Case Else
            MsgBox "نوع البيانات غير مدعوم", vbExclamation
            Exit Function
    End Select
    
    ' تحديد مسار التصدير
    مسار_التصدير = CurrentProject.Path & "\التصدير\" & اسم_الملف
    
    ' إنشاء مجلد التصدير إذا لم يكن موجوداً
    If Dir(CurrentProject.Path & "\التصدير\", vbDirectory) = "" Then
        MkDir CurrentProject.Path & "\التصدير\"
    End If
    
    ' تصدير البيانات إلى Excel
    DoCmd.TransferSpreadsheet acExport, acSpreadsheetTypeExcel12Xml, اسم_الجدول, مسار_التصدير, True
    
    MsgBox "تم تصدير البيانات بنجاح إلى:" & vbCrLf & مسار_التصدير, vbInformation, "تصدير البيانات"
    
    تصدير_البيانات_إلى_Excel = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تصدير البيانات:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    تصدير_البيانات_إلى_Excel = False
End Function

' دالة لحساب رواتب المدرسين لشهر معين
Public Function حساب_رواتب_المدرسين_الشهرية(الشهر As Integer, السنة As Integer) As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As Database
    Dim rs_مدرسين As Recordset
    Dim rs_راتب As Recordset
    Dim معرف_المدرس As Long
    Dim نوع_الراتب As String
    Dim راتب_المدرس As Currency
    Dim عدد_الطلاب As Integer
    Dim إجمالي_الأقساط As Currency
    
    Set db = CurrentDb()
    Set rs_مدرسين = db.OpenRecordset("SELECT * FROM المدرسين WHERE حالة_المدرس = 'نشط'")
    
    ' التحقق من وجود حسابات سابقة للشهر
    If DCount("*", "رواتب_المدرسين", "الشهر = " & الشهر & " AND السنة = " & السنة) > 0 Then
        If MsgBox("توجد حسابات رواتب سابقة لهذا الشهر. هل تريد إعادة الحساب؟", vbYesNo + vbQuestion) = vbNo Then
            Exit Function
        Else
            ' حذف الحسابات السابقة
            db.Execute "DELETE FROM رواتب_المدرسين WHERE الشهر = " & الشهر & " AND السنة = " & السنة
        End If
    End If
    
    ' حساب راتب كل مدرس
    Do Until rs_مدرسين.EOF
        معرف_المدرس = rs_مدرسين!معرف_المدرس
        نوع_الراتب = Nz(rs_مدرسين!نوع_الراتب, "نسبة")
        
        ' حساب عدد الطلاب والأقساط
        عدد_الطلاب = DCount("*", "الطلاب", "معرف_المدرس = " & معرف_المدرس & " AND حالة_الطالب = 'نشط'")
        
        ' حساب إجمالي الأقساط المدفوعة للمدرس في الشهر
        إجمالي_الأقساط = Nz(DSum("المبلغ_المدفوع", "الأقساط_المدفوعة ap INNER JOIN الطلاب p ON ap.معرف_الطالب = p.معرف_الطالب", "p.معرف_المدرس = " & معرف_المدرس & " AND Month(ap.تاريخ_الدفع) = " & الشهر & " AND Year(ap.تاريخ_الدفع) = " & السنة), 0)
        
        ' حساب الراتب
        راتب_المدرس = حساب_راتب_المدرس(معرف_المدرس, الشهر, السنة)
        
        ' إدراج سجل الراتب
        Set rs_راتب = db.OpenRecordset("رواتب_المدرسين", dbOpenDynaset)
        rs_راتب.AddNew
        rs_راتب!معرف_المدرس = معرف_المدرس
        rs_راتب!الشهر = الشهر
        rs_راتب!السنة = السنة
        rs_راتب!عدد_الطلاب = عدد_الطلاب
        rs_راتب!إجمالي_الأقساط = إجمالي_الأقساط
        rs_راتب!نسبة_المدرس = rs_مدرسين!نسبة_المدرس
        rs_راتب!مبلغ_الراتب = IIf(نوع_الراتب = "نسبة", راتب_المدرس, 0)
        rs_راتب!الراتب_الثابت = IIf(نوع_الراتب <> "نسبة", rs_مدرسين!الراتب_الثابت, 0)
        rs_راتب!المبلغ_الإجمالي = راتب_المدرس
        rs_راتب!حالة_الدفع = "مستحق"
        rs_راتب.Update
        rs_راتب.Close
        
        rs_مدرسين.MoveNext
    Loop
    
    rs_مدرسين.Close
    Set rs_مدرسين = Nothing
    Set db = Nothing
    
    MsgBox "تم حساب رواتب المدرسين لشهر " & الشهر & "/" & السنة & " بنجاح", vbInformation
    
    حساب_رواتب_المدرسين_الشهرية = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء حساب الرواتب:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    حساب_رواتب_المدرسين_الشهرية = False
End Function

' دالة لإرسال تذكيرات الأقساط المستحقة
Public Function إرسال_تذكيرات_الأقساط() As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As Database
    Dim rs As Recordset
    Dim sql As String
    Dim رسالة_التذكير As String
    Dim عدد_التذكيرات As Integer
    
    Set db = CurrentDb()
    
    ' استعلام الطلاب المتأخرين في الدفع
    sql = "SELECT اسم_الطالب, رقم_الهاتف, هاتف_ولي_الامر, اسم_الدورة, المبلغ_المتبقي, أيام_التسجيل " & _
          "FROM استعلام_الأقساط_المستحقة " & _
          "WHERE حالة_الاستحقاق = 'متأخر' AND المبلغ_المتبقي > 0"
    
    Set rs = db.OpenRecordset(sql)
    
    عدد_التذكيرات = 0
    
    ' إنشاء تذكير لكل طالب
    Do Until rs.EOF
        رسالة_التذكير = "عزيزي ولي أمر الطالب " & rs!اسم_الطالب & vbCrLf & _
                          "نذكركم بأن هناك مبلغ مستحق قدره " & Format(rs!المبلغ_المتبقي, "Currency") & vbCrLf & _
                          "لدورة " & rs!اسم_الدورة & vbCrLf & _
                          "يرجى المراجعة في أقرب وقت ممكن" & vbCrLf & _
                          "شكراً لكم"
        
        ' هنا يمكن إضافة كود إرسال SMS أو WhatsApp
        ' مؤقتاً سنقوم بحفظ التذكيرات في جدول
        Call حفظ_تذكير(rs!اسم_الطالب, rs!رقم_الهاتف, rs!هاتف_ولي_الامر, رسالة_التذكير)
        
        عدد_التذكيرات = عدد_التذكيرات + 1
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox "تم إنشاء " & عدد_التذكيرات & " تذكير للأقساط المستحقة", vbInformation
    
    إرسال_تذكيرات_الأقساط = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إرسال التذكيرات:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    إرسال_تذكيرات_الأقساط = False
End Function

' دالة مساعدة لحفظ التذكيرات
Private Sub حفظ_تذكير(اسم_الطالب As String, رقم_الهاتف As String, هاتف_ولي_الامر As String, رسالة_التذكير As String)
    Dim db As Database
    Dim rs As Recordset
    
    Set db = CurrentDb()
    
    ' إنشاء جدول التذكيرات إذا لم يكن موجوداً
    On Error Resume Next
    Set rs = db.OpenRecordset("التذكيرات")
    If Err.Number <> 0 Then
        ' إنشاء جدول التذكيرات
        db.Execute "CREATE TABLE التذكيرات (معرف_التذكير AUTOINCREMENT PRIMARY KEY, اسم_الطالب TEXT(100), رقم_الهاتف TEXT(20), هاتف_ولي_الامر TEXT(20), رسالة_التذكير MEMO, تاريخ_الإنشاء DATETIME DEFAULT Now(), حالة_الإرسال TEXT(20) DEFAULT 'في_الانتظار')"
    End If
    On Error GoTo 0
    
    Set rs = db.OpenRecordset("التذكيرات", dbOpenDynaset)
    rs.AddNew
    rs!اسم_الطالب = اسم_الطالب
    rs!رقم_الهاتف = رقم_الهاتف
    rs!هاتف_ولي_الامر = هاتف_ولي_الامر
    rs!رسالة_التذكير = رسالة_التذكير
    rs!تاريخ_الإنشاء = Now()
    rs!حالة_الإرسال = "في_الانتظار"
    rs.Update
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Sub

' دالة لضغط وإصلاح قاعدة البيانات
Public Function ضغط_وإصلاح_قاعدة_البيانات() As Boolean
    On Error GoTo ErrorHandler
    
    Dim مسار_قاعدة_البيانات As String
    Dim مسار_النسخة_المضغوطة As String
    
    مسار_قاعدة_البيانات = CurrentProject.FullName
    مسار_النسخة_المضغوطة = CurrentProject.Path & "\نظام_محاسبة_المعهد_مضغوط.accdb"
    
    ' إنشاء نسخة احتياطية أولاً
    If Not إنشاء_نسخة_احتياطية() Then
        MsgBox "فشل في إنشاء نسخة احتياطية. سيتم إلغاء عملية الضغط.", vbExclamation
        Exit Function
    End If
    
    ' ضغط وإصلاح قاعدة البيانات
    DBEngine.CompactDatabase مسار_قاعدة_البيانات, مسار_النسخة_المضغوطة
    
    MsgBox "تم ضغط وإصلاح قاعدة البيانات بنجاح" & vbCrLf & "النسخة المضغوطة: " & مسار_النسخة_المضغوطة, vbInformation
    
    ضغط_وإصلاح_قاعدة_البيانات = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء ضغط وإصلاح قاعدة البيانات:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    ضغط_وإصلاح_قاعدة_البيانات = False
End Function

' دالة لإنشاء تقرير الأداء الشهري
Public Function إنشاء_تقرير_الأداء_الشهري(الشهر As Integer, السنة As Integer) As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As Database
    Dim rs As Recordset
    Dim تقرير_الأداء As String
    Dim إجمالي_الإيرادات As Currency
    Dim إجمالي_المصاريف As Currency
    Dim إجمالي_الرواتب As Currency
    Dim صافي_الربح As Currency
    Dim عدد_الطلاب_الجدد As Integer
    Dim عدد_الطلاب_المنسحبين As Integer
    
    Set db = CurrentDb()
    
    ' جمع البيانات
    إجمالي_الإيرادات = إجمالي_الإيرادات_الشهرية(الشهر, السنة)
    إجمالي_المصاريف = إجمالي_المصاريف_الشهرية(الشهر, السنة)
    إجمالي_الرواتب = Nz(DSum("المبلغ_الإجمالي", "رواتب_المدرسين", "الشهر = " & الشهر & " AND السنة = " & السنة), 0)
    صافي_الربح = إجمالي_الإيرادات - إجمالي_المصاريف - إجمالي_الرواتب
    
    عدد_الطلاب_الجدد = DCount("*", "الطلاب", "Month(تاريخ_التسجيل) = " & الشهر & " AND Year(تاريخ_التسجيل) = " & السنة)
    عدد_الطلاب_المنسحبين = DCount("*", "الطلاب", "Month(تاريخ_الانسحاب) = " & الشهر & " AND Year(تاريخ_الانسحاب) = " & السنة)
    
    ' إنشاء التقرير النصي
    تقرير_الأداء = "تقرير الأداء الشهري - " & الشهر & "/" & السنة & vbCrLf & _
                    "======================================" & vbCrLf & vbCrLf & _
                    "الإحصائيات المالية:" & vbCrLf & _
                    "إجمالي الإيرادات: " & Format(إجمالي_الإيرادات, "Currency") & vbCrLf & _
                    "إجمالي المصاريف: " & Format(إجمالي_المصاريف, "Currency") & vbCrLf & _
                    "إجمالي الرواتب: " & Format(إجمالي_الرواتب, "Currency") & vbCrLf & _
                    "صافي الربح: " & Format(صافي_الربح, "Currency") & vbCrLf & vbCrLf & _
                    "إحصائيات الطلاب:" & vbCrLf & _
                    "عدد الطلاب الجدد: " & عدد_الطلاب_الجدد & vbCrLf & _
                    "عدد الطلاب المنسحبين: " & عدد_الطلاب_المنسحبين & vbCrLf & _
                    "عدد الطلاب النشطين: " & عدد_الطلاب_النشطين() & vbCrLf & vbCrLf & _
                    "تاريخ إنشاء التقرير: " & Format(Now(), "dd/mm/yyyy hh:nn")
    
    ' حفظ التقرير في ملف نصي
    Dim مسار_التقرير As String
    Dim رقم_الملف As Integer
    
    مسار_التقرير = CurrentProject.Path & "\التقارير\تقرير_الأداء_" & الشهر & "_" & السنة & ".txt"
    
    ' إنشاء مجلد التقارير إذا لم يكن موجوداً
    If Dir(CurrentProject.Path & "\التقارير\", vbDirectory) = "" Then
        MkDir CurrentProject.Path & "\التقارير\"
    End If
    
    رقم_الملف = FreeFile
    Open مسار_التقرير For Output As #رقم_الملف
    Print #رقم_الملف, تقرير_الأداء
    Close #رقم_الملف
    
    MsgBox "تم إنشاء تقرير الأداء الشهري بنجاح" & vbCrLf & "مسار التقرير: " & مسار_التقرير, vbInformation
    
    إنشاء_تقرير_الأداء_الشهري = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء تقرير الأداء:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    إنشاء_تقرير_الأداء_الشهري = False
End Function

' دالة لتحديث أرقام الإيصالات تلقائياً
Public Function تحديث_أرقام_الإيصالات() As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As Database
    Dim rs As Recordset
    Dim رقم_الإيصال_التالي As Long
    
    Set db = CurrentDb()
    
    ' العثور على آخر رقم إيصال
    Set rs = db.OpenRecordset("SELECT MAX(Val([رقم_الإيصال])) AS آخر_رقم FROM الأقساط_المدفوعة WHERE رقم_الإيصال IS NOT NULL")
    
    If Not rs.EOF And Not IsNull(rs!آخر_رقم) Then
        رقم_الإيصال_التالي = rs!آخر_رقم + 1
    Else
        رقم_الإيصال_التالي = 1001 ' رقم البداية
    End If
    
    rs.Close
    
    ' تحديث الإيصالات التي لا تحتوي على أرقام
    Set rs = db.OpenRecordset("SELECT * FROM الأقساط_المدفوعة WHERE رقم_الإيصال IS NULL OR رقم_الإيصال = '' ORDER BY معرف_الدفع")
    
    Do Until rs.EOF
        rs.Edit
        rs!رقم_الإيصال = CStr(رقم_الإيصال_التالي)
        rs.Update
        
        رقم_الإيصال_التالي = رقم_الإيصال_التالي + 1
        rs.MoveNext
    Loop
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
    
    MsgBox "تم تحديث أرقام الإيصالات بنجاح", vbInformation
    
    تحديث_أرقام_الإيصالات = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تحديث أرقام الإيصالات:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    تحديث_أرقام_الإيصالات = False
End Function

' دالة لتنظيف البيانات القديمة
Public Function تنظيف_البيانات_القديمة(عدد_السنوات As Integer) As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As Database
    Dim تاريخ_القطع As Date
    Dim عدد_السجلات_المحذوفة As Integer
    
    Set db = CurrentDb()
    تاريخ_القطع = DateAdd("yyyy", -عدد_السنوات, Now())
    
    ' تأكيد من المستخدم
    If MsgBox("هل أنت متأكد من حذف البيانات الأقدم من " & Format(تاريخ_القطع, "dd/mm/yyyy") & "؟" & vbCrLf & "هذا الإجراء لا يمكن التراجع عنه!", vbYesNo + vbQuestion + vbDefaultButton2) = vbNo Then
        Exit Function
    End If
    
    ' إنشاء نسخة احتياطية أولاً
    If Not إنشاء_نسخة_احتياطية() Then
        MsgBox "فشل في إنشاء نسخة احتياطية. سيتم إلغاء عملية التنظيف.", vbExclamation
        Exit Function
    End If
    
    ' حذف الأقساط المدفوعة القديمة
    عدد_السجلات_المحذوفة = DCount("*", "الأقساط_المدفوعة", "تاريخ_الدفع < #" & Format(تاريخ_القطع, "mm/dd/yyyy") & "#")
    db.Execute "DELETE FROM الأقساط_المدفوعة WHERE تاريخ_الدفع < #" & Format(تاريخ_القطع, "mm/dd/yyyy") & "#"
    
    ' حذف المصاريف القديمة
    عدد_السجلات_المحذوفة = عدد_السجلات_المحذوفة + DCount("*", "المصاريف", "تاريخ_المصروف < #" & Format(تاريخ_القطع, "mm/dd/yyyy") & "#")
    db.Execute "DELETE FROM المصاريف WHERE تاريخ_المصروف < #" & Format(تاريخ_القطع, "mm/dd/yyyy") & "#"
    
    ' حذف رواتب المدرسين القديمة
    عدد_السجلات_المحذوفة = عدد_السجلات_المحذوفة + DCount("*", "رواتب_المدرسين", "السنة < " & Year(تاريخ_القطع))
    db.Execute "DELETE FROM رواتب_المدرسين WHERE السنة < " & Year(تاريخ_القطع)
    
    ' حذف انسحابات الطلاب القديمة
    عدد_السجلات_المحذوفة = عدد_السجلات_المحذوفة + DCount("*", "انسحابات_الطلاب", "تاريخ_الانسحاب < #" & Format(تاريخ_القطع, "mm/dd/yyyy") & "#")
    db.Execute "DELETE FROM انسحابات_الطلاب WHERE تاريخ_الانسحاب < #" & Format(تاريخ_القطع, "mm/dd/yyyy") & "#"
    
    Set db = Nothing
    
    MsgBox "تم حذف " & عدد_السجلات_المحذوفة & " سجل قديم بنجاح", vbInformation
    
    تنظيف_البيانات_القديمة = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء تنظيف البيانات:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    تنظيف_البيانات_القديمة = False
End Function

' دالة لإنشاء فهارس لتحسين الأداء
Public Function إنشاء_فهارس_للأداء() As Boolean
    On Error GoTo ErrorHandler
    
    Dim db As Database
    Set db = CurrentDb()
    
    ' إنشاء فهارس للجداول الرئيسية
    
    ' فهرس على اسم الطالب
    On Error Resume Next
    db.Execute "CREATE INDEX idx_اسم_الطالب ON الطلاب (اسم_الطالب)"
    
    ' فهرس على حالة الطالب
    db.Execute "CREATE INDEX idx_حالة_الطالب ON الطلاب (حالة_الطالب)"
    
    ' فهرس على تاريخ الدفع
    db.Execute "CREATE INDEX idx_تاريخ_الدفع ON الأقساط_المدفوعة (تاريخ_الدفع)"
    
    ' فهرس على اسم المدرس
    db.Execute "CREATE INDEX idx_اسم_المدرس ON المدرسين (اسم_المدرس)"
    
    ' فهرس على حالة المدرس
    db.Execute "CREATE INDEX idx_حالة_المدرس ON المدرسين (حالة_المدرس)"
    
    ' فهرس على تاريخ المصروف
    db.Execute "CREATE INDEX idx_تاريخ_المصروف ON المصاريف (تاريخ_المصروف)"
    
    ' فهرس على الشهر والسنة في رواتب المدرسين
    db.Execute "CREATE INDEX idx_شهر_سنة_راتب ON رواتب_المدرسين (الشهر, السنة)"
    
    On Error GoTo 0
    
    Set db = Nothing
    
    MsgBox "تم إنشاء الفهارس بنجاح لتحسين الأداء", vbInformation
    
    إنشاء_فهارس_للأداء = True
    Exit Function
    
ErrorHandler:
    MsgBox "حدث خطأ أثناء إنشاء الفهارس:" & vbCrLf & Err.Description, vbCritical, "خطأ"
    إنشاء_فهارس_للأداء = False
End Function