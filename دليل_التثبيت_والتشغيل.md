# دليل التثبيت والتشغيل - نظام محاسبة المعاهد الأهلية العراقية

## متطلبات النظام

### الحد الأدنى المطلوب:
- نظام التشغيل: Windows 10 أو أحدث
- Microsoft Access 2016 أو أحدث
- ذاكرة الوصول العشوائي: 4 جيجابايت على الأقل
- مساحة القرص الصلب: 500 ميجابايت للنظام والبيانات

### الموصى به:
- نظام التشغيل: Windows 11
- Microsoft Access 2021 أو Microsoft 365
- ذاكرة الوصول العشوائي: 8 جيجابايت أو أكثر
- مساحة القرص الصلب: 2 جيجابايت أو أكثر

## خطوات التثبيت

### 1. إن<PERSON><PERSON><PERSON> قاعدة البيانات
1. افتح Microsoft Access
2. اختر "إنشاء قاعدة بيانات فارغة"
3. اسم الملف: `نظام_محاسبة_المعهد.accdb`
4. حفظ الملف في مجلد مناسب

### 2. إنشاء الجداول
1. في قاعدة البيانات الجديدة، اذهب إلى "إنشاء" → "تصميم الجدول"
2. قم بإنشاء الجداول التالية حسب الهيكل المحدد في `database_structure.sql`:

#### جدول اعدادات_المعهد:
```
معرف_الاعداد - رقم تلقائي (مفتاح أساسي)
اسم_المعهد - نص (100 حرف)
عنوان_المعهد - نص (255 حرف)
رقم_الهاتف - نص (20 حرف)
البريد_الالكتروني - نص (100 حرف)
شعار_المعهد - كائن OLE
نسبة_المعهد_افتراضية - رقم (مزدوج)
نسبة_المدرس_افتراضية - رقم (مزدوج)
تاريخ_انشاء_النظام - تاريخ/وقت
ملاحظات - نص طويل
```

#### جدول الدورات:
```
معرف_الدورة - رقم تلقائي (مفتاح أساسي)
اسم_الدورة - نص (100 حرف)
وصف_الدورة - نص (255 حرف)
مدة_الدورة - رقم (صحيح)
رسوم_الدورة - عملة
حالة_الدورة - نص (20 حرف)
تاريخ_الانشاء - تاريخ/وقت
```

#### جدول المدرسين:
```
معرف_المدرس - رقم تلقائي (مفتاح أساسي)
اسم_المدرس - نص (100 حرف)
رقم_الهاتف - نص (20 حرف)
العنوان - نص (255 حرف)
التخصص - نص (100 حرف)
نوع_الراتب - نص (20 حرف)
الراتب_الثابت - عملة
نسبة_المدرس - رقم (مزدوج)
تاريخ_التعيين - تاريخ/وقت
حالة_المدرس - نص (20 حرف)
ملاحظات - نص طويل
```

#### جدول الطلاب:
```
معرف_الطالب - رقم تلقائي (مفتاح أساسي)
اسم_الطالب - نص (100 حرف)
الجنس - نص (10 حرف)
العمر - رقم (صحيح)
رقم_الهاتف - نص (20 حرف)
هاتف_ولي_الامر - نص (20 حرف)
العنوان - نص (255 حرف)
الصف_الدراسي - نص (50 حرف)
معرف_الدورة - رقم (صحيح)
معرف_المدرس - رقم (صحيح)
رسوم_الدورة - عملة
المبلغ_المدفوع - عملة
المبلغ_المتبقي - عملة
نسبة_الخصم - رقم (مزدوج)
تاريخ_التسجيل - تاريخ/وقت
حالة_الطالب - نص (20 حرف)
تاريخ_الانسحاب - تاريخ/وقت
مبلغ_الاسترداد - عملة
ملاحظات - نص طويل
```

#### جدول الأقساط_المدفوعة:
```
معرف_الدفع - رقم تلقائي (مفتاح أساسي)
معرف_الطالب - رقم (صحيح)
المبلغ_المدفوع - عملة
تاريخ_الدفع - تاريخ/وقت
طريقة_الدفع - نص (20 حرف)
رقم_الإيصال - نص (20 حرف)
الملاحظات - نص (255 حرف)
```

#### جدول المصاريف:
```
معرف_المصروف - رقم تلقائي (مفتاح أساسي)
نوع_المصروف - نص (100 حرف)
المبلغ - عملة
تاريخ_المصروف - تاريخ/وقت
وصف_المصروف - نص (255 حرف)
فئة_المصروف - نص (50 حرف)
الشهر - رقم (صحيح)
السنة - رقم (صحيح)
ملاحظات - نص طويل
```

#### جدول رواتب_المدرسين:
```
معرف_الراتب - رقم تلقائي (مفتاح أساسي)
معرف_المدرس - رقم (صحيح)
الشهر - رقم (صحيح)
السنة - رقم (صحيح)
عدد_الطلاب - رقم (صحيح)
إجمالي_الأقساط - عملة
نسبة_المدرس - رقم (مزدوج)
مبلغ_الراتب - عملة
الراتب_الثابت - عملة
المبلغ_الإجمالي - عملة
تاريخ_الدفع - تاريخ/وقت
حالة_الدفع - نص (20 حرف)
ملاحظات - نص (255 حرف)
```

#### جدول انسحابات_الطلاب:
```
معرف_الانسحاب - رقم تلقائي (مفتاح أساسي)
معرف_الطالب - رقم (صحيح)
تاريخ_الانسحاب - تاريخ/وقت
سبب_الانسحاب - نص (255 حرف)
المبلغ_المدفوع_إجمالي - عملة
نسبة_الخصم - رقم (مزدوج)
المبلغ_المسترد - عملة
المبلغ_المحتفظ_به - عملة
ملاحظات - نص طويل
```

### 3. إنشاء العلاقات
1. اذهب إلى "أدوات قاعدة البيانات" → "علاقات"
2. أضف الجداول التالية: الطلاب، الدورات، المدرسين، الأقساط_المدفوعة، رواتب_المدرسين، انسحابات_الطلاب
3. أنشئ العلاقات التالية:
   - الطلاب.معرف_الدورة ← الدورات.معرف_الدورة
   - الطلاب.معرف_المدرس ← المدرسين.معرف_المدرس
   - الأقساط_المدفوعة.معرف_الطالب ← الطلاب.معرف_الطالب
   - رواتب_المدرسين.معرف_المدرس ← المدرسين.معرف_المدرس
   - انسحابات_الطلاب.معرف_الطالب ← الطلاب.معرف_الطالب

### 4. إدخال البيانات الأولية
1. افتح جدول "اعدادات_المعهد" وأدخل:
   - اسم_المعهد: "معهد النور الأهلي"
   - عنوان_المعهد: "بغداد - العراق"
   - رقم_الهاتف: "07701234567"
   - نسبة_المعهد_افتراضية: 0.7
   - نسبة_المدرس_افتراضية: 0.3

2. افتح جدول "الدورات" وأدخل الدورات التالية:
   - اللغة الإنجليزية - مستوى أول (150,000 دينار)
   - الرياضيات - الثالث متوسط (200,000 دينار)
   - الفيزياء - السادس العلمي (250,000 دينار)
   - الكيمياء - السادس العلمي (250,000 دينار)
   - الحاسوب - مبتدئ (100,000 دينار)

### 5. إنشاء الوحدات النمطية (VBA)
1. اذهب إلى "إنشاء" → "وحدة نمطية"
2. انسخ محتوى ملف `VBA_Modules.vba` إلى الوحدة النمطية
3. احفظ الوحدة باسم "الوظائف_العامة"

### 6. إنشاء النماذج
قم بإنشاء النماذج التالية حسب التصميم المحدد في `Forms_Design.xml`:

#### النموذج الرئيسي (الواجهة_الرئيسية):
1. "إنشاء" → "تصميم النموذج"
2. اضبط خصائص النموذج:
   - العنوان: "نظام محاسبة المعاهد الأهلية العراقية"
   - لون الخلفية: #F0F0F0
   - اتجاه النص: من اليمين إلى اليسار
3. أضف العناصر التالية:
   - تسمية لعنوان النظام
   - مربعات إحصائيات (عدد الطلاب، المدرسين، الأرباح، الأقساط غير المدفوعة)
   - أزرار التنقل (إدارة الطلاب، المدرسين، الحسابات، التقارير، الإعدادات)

#### نموذج إدارة الطلاب (نموذج_الطلاب):
1. "إنشاء" → "تصميم النموذج"
2. اضبط مصدر السجلات: "الطلاب"
3. أضف الحقول التالية:
   - اسم الطالب، الجنس، العمر، رقم الهاتف
   - هاتف ولي الأمر، العنوان، الصف الدراسي
   - الدورة (قائمة منسدلة)، المدرس (قائمة منسدلة)
   - رسوم الدورة، المبلغ المدفوع، المبلغ المتبقي
4. أضف الأزرار: طالب جديد، حفظ، حذف، انسحاب، دفع قسط

#### نموذج إدارة المدرسين (نموذج_المدرسين):
1. "إنشاء" → "تصميم النموذج"
2. اضبط مصدر السجلات: "المدرسين"
3. أضف الحقول التالية:
   - اسم المدرس، التخصص، رقم الهاتف، العنوان
   - نوع الراتب، الراتب الثابت، نسبة المدرس
   - تاريخ التعيين، حالة المدرس

#### نموذج الحسابات (نموذج_الحسابات):
1. "إنشاء" → "تصميم النموذج"
2. أضف أقسام:
   - تسجيل الدفعات الجديدة
   - تسجيل المصاريف
   - عرض الإحصائيات المالية

### 7. إنشاء التقارير
قم بإنشاء التقارير التالية حسب التصميم المحدد في `Reports_Design.xml`:

#### تقرير الطلاب الشامل:
1. "إنشاء" → "تصميم التقرير"
2. اضبط مصدر السجلات لتشمل معلومات الطلاب مع الدورات والمدرسين
3. أضف أقسام: رأس التقرير، رأس الصفحة، التفاصيل، ذيل التقرير، ذيل الصفحة

#### تقرير الأقساط المدفوعة:
1. "إنشاء" → "تصميم التقرير"
2. اضبط مصدر السجلات لتشمل معلومات الدفعات مع أسماء الطلاب
3. أضف المجاميع والإحصائيات

#### تقرير رواتب المدرسين:
1. "إنشاء" → "تصميم التقرير"
2. اضبط مصدر السجلات لتشمل معلومات الرواتب مع أسماء المدرسين
3. أضف المجاميع والإحصائيات

### 8. إنشاء الاستعلامات
قم بإنشاء الاستعلامات التالية:

#### استعلام_الطلاب:
```sql
SELECT 
    p.معرف_الطالب,
    p.اسم_الطالب,
    p.الجنس,
    p.العمر,
    p.رقم_الهاتف,
    p.الصف_الدراسي,
    c.اسم_الدورة,
    t.اسم_المدرس,
    p.رسوم_الدورة,
    p.المبلغ_المدفوع,
    p.المبلغ_المتبقي,
    p.حالة_الطالب
FROM ((الطلاب p 
LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
LEFT JOIN المدرسين t ON p.معرف_المدرس = t.معرف_المدرس)
WHERE p.حالة_الطالب = 'نشط'
ORDER BY p.اسم_الطالب;
```

#### استعلام_الأقساط_المستحقة:
```sql
SELECT 
    p.معرف_الطالب,
    p.اسم_الطالب,
    p.رقم_الهاتف,
    p.هاتف_ولي_الامر,
    c.اسم_الدورة,
    p.المبلغ_المتبقي,
    p.تاريخ_التسجيل
FROM (الطلاب p 
LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
WHERE p.حالة_الطالب = 'نشط' AND p.المبلغ_المتبقي > 0
ORDER BY p.المبلغ_المتبقي DESC;
```

### 9. إعداد النسخ الاحتياطي
1. أنشئ مجلد للنسخ الاحتياطية
2. أضف ماكرو للنسخ الاحتياطي التلقائي
3. اجدولة النسخ الاحتياطية اليومية

## تشغيل النظام

### 1. فتح النظام
1. انقر مرتين على ملف `نظام_محاسبة_المعهد.accdb`
2. إذا ظهر تحذير أمان، اختر "تمكين المحتوى"
3. ستفتح الواجهة الرئيسية تلقائياً

### 2. الاستخدام اليومي
1. **إضافة طالب جديد**: الواجهة الرئيسية → إدارة الطلاب → طالب جديد
2. **تسجيل دفعة**: إدارة الطلاب → اختيار الطالب → دفع قسط
3. **إضافة مصروف**: الحسابات → تسجيل مصروف جديد
4. **طباعة تقرير**: التقارير → اختيار نوع التقرير → طباعة

### 3. المهام الشهرية
1. حساب رواتب المدرسين
2. إنشاء تقرير الأرباح والخسائر
3. عمل نسخة احتياطية شهرية
4. مراجعة الأقساط المستحقة

## استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### خطأ: "تم رفض الوصول إلى قاعدة البيانات"
- الحل: تأكد من أن الملف ليس للقراءة فقط
- تأكد من وجود صلاحيات الكتابة في المجلد

#### خطأ: "لا يمكن فتح النموذج"
- الحل: تأكد من وجود جميع الجداول المطلوبة
- تحقق من صحة العلاقات بين الجداول

#### خطأ: "الوظيفة غير معرفة"
- الحل: تأكد من حفظ الوحدة النمطية VBA بشكل صحيح
- تأكد من تمكين الماكروات في إعدادات Access

#### بطء في الأداء:
- الحل: قم بضغط وإصلاح قاعدة البيانات
- أنشئ فهارس للحقول المستخدمة في البحث

### نصائح للصيانة:

1. **النسخ الاحتياطي المنتظم**:
   - يومي: نسخة سريعة
   - أسبوعي: نسخة كاملة
   - شهري: نسخة خارجية

2. **تنظيف البيانات**:
   - حذف السجلات القديمة غير المطلوبة
   - ضغط قاعدة البيانات شهرياً

3. **مراقبة الأداء**:
   - متابعة سرعة النظام
   - تحديث الفهارس عند الحاجة

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من قسم استكشاف الأخطاء
3. راجع ملفات VBA للوظائف المخصصة
4. تواصل مع فريق التطوير عند الحاجة

## تحديثات النظام

### الإصدار 1.0 (الحالي):
- الوظائف الأساسية للمحاسبة
- إدارة الطلاب والمدرسين
- التقارير الأساسية

### التحديثات المستقبلية:
- إضافة نظام الرسائل النصية
- تكامل مع أنظمة الدفع الإلكتروني
- تطبيق الهاتف المحمول
- النسخ الاحتياطي السحابي

---

**ملاحظة هامة**: تأكد من إجراء نسخة احتياطية قبل إجراء أي تحديثات أو تعديلات على النظام.