' سكريبت إنشاء قاعدة بيانات Access لنظام محاسبة المعاهد
' Iraqi Private Institutes Accounting System - Database Creation Script

Dim accessApp, db, tbl, fld, rel

' إنشاء تطبيق Access
Set accessApp = CreateObject("Access.Application")

' إنشاء قاعدة بيانات جديدة
accessApp.NewCurrentDatabase "نظام_محاسبة_المعاهد.accdb"
Set db = accessApp.CurrentDb

' إنشاء جدول إعدادات المعهد
Set tbl = db.CreateTableDef("إعدادات_المعهد")
tbl.Fields.Append tbl.CreateField("رقم_الإعداد", 4) ' AutoNumber
tbl.Fields("رقم_الإعداد").Attributes = 16 ' AutoIncrement
tbl.Fields.Append tbl.CreateField("اسم_المعهد", 10, 255)
tbl.Fields.Append tbl.CreateField("عنوان_المعهد", 10, 500)
tbl.Fields.Append tbl.CreateField("رقم_الهاتف", 10, 50)
tbl.Fields.Append tbl.CreateField("نسبة_المعهد", 6) ' Single
tbl.Fields.Append tbl.CreateField("مسار_الشعار", 10, 255)
tbl.Fields.Append tbl.CreateField("تاريخ_الإنشاء", 8) ' DateTime
tbl.Fields.Append tbl.CreateField("تاريخ_التعديل", 8) ' DateTime

' إنشاء المفتاح الأساسي
Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_الإعداد")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول الدورات
Set tbl = db.CreateTableDef("الدورات")
tbl.Fields.Append tbl.CreateField("رقم_الدورة", 4) ' AutoNumber
tbl.Fields("رقم_الدورة").Attributes = 16
tbl.Fields.Append tbl.CreateField("اسم_الدورة", 10, 255)
tbl.Fields.Append tbl.CreateField("وصف_الدورة", 10, 500)
tbl.Fields.Append tbl.CreateField("الرسوم_الافتراضية", 5) ' Currency
tbl.Fields.Append tbl.CreateField("المدة", 10, 100)
tbl.Fields.Append tbl.CreateField("نشطة", 1) ' Boolean
tbl.Fields.Append tbl.CreateField("تاريخ_الإنشاء", 8)

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_الدورة")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول المدرسين
Set tbl = db.CreateTableDef("المدرسين")
tbl.Fields.Append tbl.CreateField("رقم_المدرس", 4)
tbl.Fields("رقم_المدرس").Attributes = 16
tbl.Fields.Append tbl.CreateField("اسم_المدرس", 10, 255)
tbl.Fields.Append tbl.CreateField("التخصص", 10, 255)
tbl.Fields.Append tbl.CreateField("رقم_الهاتف", 10, 50)
tbl.Fields.Append tbl.CreateField("نوع_الأجر", 10, 50)
tbl.Fields.Append tbl.CreateField("الأجر_الشهري", 5)
tbl.Fields.Append tbl.CreateField("نسبة_المشاركة", 6)
tbl.Fields.Append tbl.CreateField("تاريخ_التوظيف", 8)
tbl.Fields.Append tbl.CreateField("نشط", 1)
tbl.Fields.Append tbl.CreateField("ملاحظات", 12) ' Memo

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_المدرس")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول الطلاب
Set tbl = db.CreateTableDef("الطلاب")
tbl.Fields.Append tbl.CreateField("رقم_الطالب", 4)
tbl.Fields("رقم_الطالب").Attributes = 16
tbl.Fields.Append tbl.CreateField("اسم_الطالب", 10, 255)
tbl.Fields.Append tbl.CreateField("الجنس", 10, 10)
tbl.Fields.Append tbl.CreateField("العمر", 3) ' Integer
tbl.Fields.Append tbl.CreateField("الصف", 10, 50)
tbl.Fields.Append tbl.CreateField("رقم_الدورة", 4)
tbl.Fields.Append tbl.CreateField("رقم_المدرس", 4)
tbl.Fields.Append tbl.CreateField("القسط_الكلي", 5)
tbl.Fields.Append tbl.CreateField("المبلغ_المدفوع", 5)
tbl.Fields.Append tbl.CreateField("المبلغ_المتبقي", 5)
tbl.Fields.Append tbl.CreateField("تاريخ_التسجيل", 8)
tbl.Fields.Append tbl.CreateField("حالة_الطالب", 10, 50)
tbl.Fields.Append tbl.CreateField("تاريخ_الانسحاب", 8)
tbl.Fields.Append tbl.CreateField("المبلغ_المسترجع", 5)
tbl.Fields.Append tbl.CreateField("اسم_ولي_الأمر", 10, 255)
tbl.Fields.Append tbl.CreateField("هاتف_ولي_الأمر", 10, 50)
tbl.Fields.Append tbl.CreateField("العنوان", 10, 500)
tbl.Fields.Append tbl.CreateField("ملاحظات", 12)

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_الطالب")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول الدفعات
Set tbl = db.CreateTableDef("الدفعات")
tbl.Fields.Append tbl.CreateField("رقم_الدفعة", 4)
tbl.Fields("رقم_الدفعة").Attributes = 16
tbl.Fields.Append tbl.CreateField("رقم_الطالب", 4)
tbl.Fields.Append tbl.CreateField("المبلغ_المدفوع", 5)
tbl.Fields.Append tbl.CreateField("تاريخ_الدفع", 8)
tbl.Fields.Append tbl.CreateField("طريقة_الدفع", 10, 50)
tbl.Fields.Append tbl.CreateField("رقم_الإيصال", 10, 100)
tbl.Fields.Append tbl.CreateField("ملاحظات", 12)
tbl.Fields.Append tbl.CreateField("المستخدم", 10, 100)

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_الدفعة")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول المصاريف
Set tbl = db.CreateTableDef("المصاريف")
tbl.Fields.Append tbl.CreateField("رقم_المصروف", 4)
tbl.Fields("رقم_المصروف").Attributes = 16
tbl.Fields.Append tbl.CreateField("نوع_المصروف", 10, 255)
tbl.Fields.Append tbl.CreateField("المبلغ", 5)
tbl.Fields.Append tbl.CreateField("تاريخ_المصروف", 8)
tbl.Fields.Append tbl.CreateField("الوصف", 10, 500)
tbl.Fields.Append tbl.CreateField("الشهر", 3)
tbl.Fields.Append tbl.CreateField("السنة", 3)
tbl.Fields.Append tbl.CreateField("مصروف_دوري", 1)
tbl.Fields.Append tbl.CreateField("الفئة", 10, 100)
tbl.Fields.Append tbl.CreateField("ملاحظات", 12)

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_المصروف")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول أرباح المدرسين
Set tbl = db.CreateTableDef("أرباح_المدرسين")
tbl.Fields.Append tbl.CreateField("رقم_الربح", 4)
tbl.Fields("رقم_الربح").Attributes = 16
tbl.Fields.Append tbl.CreateField("رقم_المدرس", 4)
tbl.Fields.Append tbl.CreateField("الشهر", 3)
tbl.Fields.Append tbl.CreateField("السنة", 3)
tbl.Fields.Append tbl.CreateField("عدد_الطلاب", 3)
tbl.Fields.Append tbl.CreateField("مجموع_أقساط_الطلاب", 5)
tbl.Fields.Append tbl.CreateField("نصيب_المدرس", 5)
tbl.Fields.Append tbl.CreateField("نصيب_المعهد", 5)
tbl.Fields.Append tbl.CreateField("مدفوع", 1)
tbl.Fields.Append tbl.CreateField("تاريخ_الدفع", 8)
tbl.Fields.Append tbl.CreateField("ملاحظات", 12)

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_الربح")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء جدول الانسحابات
Set tbl = db.CreateTableDef("الانسحابات")
tbl.Fields.Append tbl.CreateField("رقم_الانسحاب", 4)
tbl.Fields("رقم_الانسحاب").Attributes = 16
tbl.Fields.Append tbl.CreateField("رقم_الطالب", 4)
tbl.Fields.Append tbl.CreateField("تاريخ_الانسحاب", 8)
tbl.Fields.Append tbl.CreateField("السبب", 10, 500)
tbl.Fields.Append tbl.CreateField("المبلغ_المسترجع", 5)
tbl.Fields.Append tbl.CreateField("نسبة_الاسترجاع", 6)
tbl.Fields.Append tbl.CreateField("المعالج", 10, 100)
tbl.Fields.Append tbl.CreateField("ملاحظات", 12)

Set idx = tbl.CreateIndex("PrimaryKey")
idx.Primary = True
idx.Fields.Append idx.CreateField("رقم_الانسحاب")
tbl.Indexes.Append idx

db.TableDefs.Append tbl

' إنشاء العلاقات بين الجداول
' علاقة بين الطلاب والدورات
Set rel = db.CreateRelation("علاقة_الطلاب_الدورات")
rel.Table = "الدورات"
rel.ForeignTable = "الطلاب"
rel.Fields.Append rel.CreateField("رقم_الدورة")
rel.Fields("رقم_الدورة").ForeignName = "رقم_الدورة"
db.Relations.Append rel

' علاقة بين الطلاب والمدرسين
Set rel = db.CreateRelation("علاقة_الطلاب_المدرسين")
rel.Table = "المدرسين"
rel.ForeignTable = "الطلاب"
rel.Fields.Append rel.CreateField("رقم_المدرس")
rel.Fields("رقم_المدرس").ForeignName = "رقم_المدرس"
db.Relations.Append rel

' علاقة بين الطلاب والدفعات
Set rel = db.CreateRelation("علاقة_الطلاب_الدفعات")
rel.Table = "الطلاب"
rel.ForeignTable = "الدفعات"
rel.Fields.Append rel.CreateField("رقم_الطالب")
rel.Fields("رقم_الطالب").ForeignName = "رقم_الطالب"
db.Relations.Append rel

' إدراج البيانات الأولية
db.Execute "INSERT INTO إعدادات_المعهد (اسم_المعهد, عنوان_المعهد, رقم_الهاتف, نسبة_المعهد, تاريخ_الإنشاء) VALUES ('معهد النور الأهلي', 'بغداد - الكرادة', '07901234567', 70, Now())"

' إدراج دورات افتراضية
db.Execute "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة, نشطة, تاريخ_الإنشاء) VALUES ('دورة اللغة الإنجليزية - المستوى الأول', 'دورة تأسيسية في اللغة الإنجليزية', 150000, '3 أشهر', True, Now())"
db.Execute "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة, نشطة, تاريخ_الإنشاء) VALUES ('دورة اللغة الإنجليزية - المستوى الثاني', 'دورة متوسطة في اللغة الإنجليزية', 200000, '3 أشهر', True, Now())"
db.Execute "INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, الرسوم_الافتراضية, المدة, نشطة, تاريخ_الإنشاء) VALUES ('دورة الرياضيات - الصف السادس', 'دورة تقوية في الرياضيات للصف السادس', 120000, 'شهرين', True, Now())"

' إدراج مدرسين افتراضيين
db.Execute "INSERT INTO المدرسين (اسم_المدرس, التخصص, رقم_الهاتف, نوع_الأجر, نسبة_المشاركة, تاريخ_التوظيف, نشط) VALUES ('أ. أحمد محمد علي', 'اللغة الإنجليزية', '07701234567', 'نسبة', 30, Now(), True)"
db.Execute "INSERT INTO المدرسين (اسم_المدرس, التخصص, رقم_الهاتف, نوع_الأجر, نسبة_المشاركة, تاريخ_التوظيف, نشط) VALUES ('أ. فاطمة حسن', 'الرياضيات', '07801234567', 'نسبة', 35, Now(), True)"

' حفظ وإغلاق قاعدة البيانات
accessApp.DoCmd.Save
accessApp.Quit

MsgBox "تم إنشاء قاعدة البيانات بنجاح!" & vbCrLf & "Database created successfully!"

' تنظيف الذاكرة
Set tbl = Nothing
Set db = Nothing
Set accessApp = Nothing
