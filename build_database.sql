-- سكريب<PERSON> بناء قاعدة البيانات الكاملة
-- نظام محاسبة المعاهد الأهلية العراقية

-- حذف الجداول إذا كانت موجودة (للبناء من جديد)
DROP TABLE IF EXISTS التذكيرات;
DROP TABLE IF EXISTS انسحابات_الطلاب;
DROP TABLE IF EXISTS رواتب_المدرسين;
DROP TABLE IF EXISTS المصاريف;
DROP TABLE IF EXISTS الأقساط_المدفوعة;
DROP TABLE IF EXISTS الطلاب;
DROP TABLE IF EXISTS المدرسين;
DROP TABLE IF EXISTS الدورات;
DROP TABLE IF EXISTS اعدادات_المعهد;

-- إن<PERSON><PERSON><PERSON> جدول اعدادات المعهد
CREATE TABLE اعدادات_المعهد (
    معرف_الاعداد AUTOINCREMENT PRIMARY KEY,
    اسم_المعهد TEXT(100) NOT NULL,
    عنوان_المعهد TEXT(255),
    رقم_الهاتف TEXT(20),
    البريد_الالكتروني TEXT(100),
    شعار_المعهد LONGBINARY,
    نسبة_المعهد_افتراضية DOUBLE DEFAULT 0.7,
    نسبة_المدرس_افتراضية DOUBLE DEFAULT 0.3,
    تاريخ_انشاء_النظام DATETIME DEFAULT Now(),
    ملاحظات LONGTEXT
);

-- إنشاء جدول الدورات
CREATE TABLE الدورات (
    معرف_الدورة AUTOINCREMENT PRIMARY KEY,
    اسم_الدورة TEXT(100) NOT NULL,
    وصف_الدورة TEXT(255),
    مدة_الدورة INTEGER,
    رسوم_الدورة CURRENCY DEFAULT 0,
    حالة_الدورة TEXT(20) DEFAULT 'نشط',
    تاريخ_الانشاء DATETIME DEFAULT Now()
);

-- إنشاء جدول المدرسين
CREATE TABLE المدرسين (
    معرف_المدرس AUTOINCREMENT PRIMARY KEY,
    اسم_المدرس TEXT(100) NOT NULL,
    رقم_الهاتف TEXT(20),
    العنوان TEXT(255),
    التخصص TEXT(100),
    نوع_الراتب TEXT(20) DEFAULT 'نسبة',
    الراتب_الثابت CURRENCY DEFAULT 0,
    نسبة_المدرس DOUBLE DEFAULT 0.3,
    تاريخ_التعيين DATETIME DEFAULT Now(),
    حالة_المدرس TEXT(20) DEFAULT 'نشط',
    ملاحظات LONGTEXT
);

-- إنشاء جدول الطلاب
CREATE TABLE الطلاب (
    معرف_الطالب AUTOINCREMENT PRIMARY KEY,
    اسم_الطالب TEXT(100) NOT NULL,
    الجنس TEXT(10),
    العمر INTEGER,
    رقم_الهاتف TEXT(20),
    هاتف_ولي_الامر TEXT(20),
    العنوان TEXT(255),
    الصف_الدراسي TEXT(50),
    معرف_الدورة INTEGER,
    معرف_المدرس INTEGER,
    رسوم_الدورة CURRENCY DEFAULT 0,
    المبلغ_المدفوع CURRENCY DEFAULT 0,
    المبلغ_المتبقي CURRENCY DEFAULT 0,
    نسبة_الخصم DOUBLE DEFAULT 0,
    تاريخ_التسجيل DATETIME DEFAULT Now(),
    حالة_الطالب TEXT(20) DEFAULT 'نشط',
    تاريخ_الانسحاب DATETIME,
    مبلغ_الاسترداد CURRENCY DEFAULT 0,
    ملاحظات LONGTEXT,
    FOREIGN KEY (معرف_الدورة) REFERENCES الدورات(معرف_الدورة),
    FOREIGN KEY (معرف_المدرس) REFERENCES المدرسين(معرف_المدرس)
);

-- إنشاء جدول الأقساط المدفوعة
CREATE TABLE الأقساط_المدفوعة (
    معرف_الدفع AUTOINCREMENT PRIMARY KEY,
    معرف_الطالب INTEGER NOT NULL,
    المبلغ_المدفوع CURRENCY NOT NULL,
    تاريخ_الدفع DATETIME DEFAULT Now(),
    طريقة_الدفع TEXT(20) DEFAULT 'نقد',
    رقم_الإيصال TEXT(20),
    الملاحظات TEXT(255),
    FOREIGN KEY (معرف_الطالب) REFERENCES الطلاب(معرف_الطالب)
);

-- إنشاء جدول المصاريف
CREATE TABLE المصاريف (
    معرف_المصروف AUTOINCREMENT PRIMARY KEY,
    نوع_المصروف TEXT(100) NOT NULL,
    المبلغ CURRENCY NOT NULL,
    تاريخ_المصروف DATETIME DEFAULT Now(),
    وصف_المصروف TEXT(255),
    فئة_المصروف TEXT(50),
    الشهر INTEGER,
    السنة INTEGER,
    ملاحظات LONGTEXT
);

-- إنشاء جدول رواتب المدرسين
CREATE TABLE رواتب_المدرسين (
    معرف_الراتب AUTOINCREMENT PRIMARY KEY,
    معرف_المدرس INTEGER NOT NULL,
    الشهر INTEGER NOT NULL,
    السنة INTEGER NOT NULL,
    عدد_الطلاب INTEGER DEFAULT 0,
    إجمالي_الأقساط CURRENCY DEFAULT 0,
    نسبة_المدرس DOUBLE DEFAULT 0.3,
    مبلغ_الراتب CURRENCY DEFAULT 0,
    الراتب_الثابت CURRENCY DEFAULT 0,
    المبلغ_الإجمالي CURRENCY DEFAULT 0,
    تاريخ_الدفع DATETIME,
    حالة_الدفع TEXT(20) DEFAULT 'مستحق',
    ملاحظات TEXT(255),
    FOREIGN KEY (معرف_المدرس) REFERENCES المدرسين(معرف_المدرس)
);

-- إنشاء جدول انسحابات الطلاب
CREATE TABLE انسحابات_الطلاب (
    معرف_الانسحاب AUTOINCREMENT PRIMARY KEY,
    معرف_الطالب INTEGER NOT NULL,
    تاريخ_الانسحاب DATETIME DEFAULT Now(),
    سبب_الانسحاب TEXT(255),
    المبلغ_المدفوع_إجمالي CURRENCY DEFAULT 0,
    نسبة_الخصم DOUBLE DEFAULT 0.1,
    المبلغ_المسترد CURRENCY DEFAULT 0,
    المبلغ_المحتفظ_به CURRENCY DEFAULT 0,
    ملاحظات LONGTEXT,
    FOREIGN KEY (معرف_الطالب) REFERENCES الطلاب(معرف_الطالب)
);

-- إنشاء جدول التذكيرات
CREATE TABLE التذكيرات (
    معرف_التذكير AUTOINCREMENT PRIMARY KEY,
    اسم_الطالب TEXT(100),
    رقم_الهاتف TEXT(20),
    هاتف_ولي_الامر TEXT(20),
    رسالة_التذكير LONGTEXT,
    تاريخ_الإنشاء DATETIME DEFAULT Now(),
    حالة_الإرسال TEXT(20) DEFAULT 'في_الانتظار'
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX idx_اسم_الطالب ON الطلاب (اسم_الطالب);
CREATE INDEX idx_حالة_الطالب ON الطلاب (حالة_الطالب);
CREATE INDEX idx_تاريخ_الدفع ON الأقساط_المدفوعة (تاريخ_الدفع);
CREATE INDEX idx_اسم_المدرس ON المدرسين (اسم_المدرس);
CREATE INDEX idx_حالة_المدرس ON المدرسين (حالة_المدرس);
CREATE INDEX idx_تاريخ_المصروف ON المصاريف (تاريخ_المصروف);
CREATE INDEX idx_شهر_سنة_راتب ON رواتب_المدرسين (الشهر, السنة);

-- إدراج البيانات التجريبية
-- (سيتم تشغيل sample_data.sql بعد ذلك)

-- تحديث الأرقام التلقائية
-- تحديث المبلغ المتبقي للطلاب
UPDATE الطلاب SET المبلغ_المتبقي = رسوم_الدورة - المبلغ_المدفوع;

-- تحديث الشهر والسنة في المصاريف
UPDATE المصاريف SET الشهر = Month(تاريخ_المصروف), السنة = Year(تاريخ_المصروف) WHERE الشهر IS NULL OR السنة IS NULL;

COMMIT;