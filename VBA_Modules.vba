' ===========================================
' نظام محاسبة المعاهد الأهلية العراقية
' VBA Code Modules
' ===========================================

Option Compare Database
Option Explicit

' ===========================================
' Module: الوظائف_العامة
' ===========================================

' دالة لتنسيق الأرقام العربية
Public Function تنسيق_رقم_عربي(رقم As Variant) As String
    If IsNull(رقم) Then
        تنسيق_رقم_عربي = "0"
    Else
        تنسيق_رقم_عربي = Format(رقم, "#,##0")
    End If
End Function

' دالة لتحويل الأرقام إلى كلمات باللغة العربية (مبسطة)
Public Function رقم_إلى_كلمات(رقم As Long) As String
    Dim آحاد As Variant
    Dim عشرات As Variant
    Dim مئات As Variant
    
    آحاد = Array("", "واحد", "اثنان", "ثلاثة", "أربعة", "خمسة", "ستة", "سبعة", "ثمانية", "تسعة")
    عشرات = Array("", "عشرة", "عشرون", "ثلاثون", "أربعون", "خمسون", "ستون", "سبعون", "ثمانون", "تسعون")
    مئات = Array("", "مائة", "مائتان", "ثلاثمائة", "أربعمائة", "خمسمائة", "ستمائة", "سبعمائة", "ثمانمائة", "تسعمائة")
    
    If رقم = 0 Then
        رقم_إلى_كلمات = "صفر"
    ElseIf رقم < 10 Then
        رقم_إلى_كلمات = آحاد(رقم)
    ElseIf رقم < 100 Then
        رقم_إلى_كلمات = عشرات(رقم \ 10) & " " & آحاد(رقم Mod 10)
    ElseIf رقم < 1000 Then
        رقم_إلى_كلمات = مئات(رقم \ 100) & " " & عشرات((رقم Mod 100) \ 10) & " " & آحاد(رقم Mod 10)
    Else
        رقم_إلى_كلمات = "أكثر من ألف"
    End If
    
    رقم_إلى_كلمات = Trim(رقم_إلى_كلمات)
End Function

' دالة لحساب المبلغ المتبقي للطالب
Public Function حساب_المبلغ_المتبقي(معرف_الطالب As Long) As Currency
    Dim db As Database
    Dim rs As Recordset
    Dim رسوم_الدورة As Currency
    Dim المبلغ_المدفوع As Currency
    
    Set db = CurrentDb()
    Set rs = db.OpenRecordset("SELECT رسوم_الدورة, المبلغ_المدفوع FROM الطلاب WHERE معرف_الطالب = " & معرف_الطالب)
    
    If Not rs.EOF Then
        رسوم_الدورة = Nz(rs!رسوم_الدورة, 0)
        المبلغ_المدفوع = Nz(rs!المبلغ_المدفوع, 0)
        حساب_المبلغ_المتبقي = رسوم_الدورة - المبلغ_المدفوع
    Else
        حساب_المبلغ_المتبقي = 0
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لحساب راتب المدرس
Public Function حساب_راتب_المدرس(معرف_المدرس As Long, الشهر As Integer, السنة As Integer) As Currency
    Dim db As Database
    Dim rs As Recordset
    Dim sql As String
    Dim نوع_الراتب As String
    Dim الراتب_الثابت As Currency
    Dim نسبة_المدرس As Double
    Dim إجمالي_الأقساط As Currency
    
    Set db = CurrentDb()
    
    ' جلب بيانات المدرس
    Set rs = db.OpenRecordset("SELECT نوع_الراتب, الراتب_الثابت, نسبة_المدرس FROM المدرسين WHERE معرف_المدرس = " & معرف_المدرس)
    
    If Not rs.EOF Then
        نوع_الراتب = Nz(rs!نوع_الراتب, "نسبة")
        الراتب_الثابت = Nz(rs!الراتب_الثابت, 0)
        نسبة_المدرس = Nz(rs!نسبة_المدرس, 0.3)
    End If
    rs.Close
    
    ' حساب إجمالي الأقساط المدفوعة للمدرس في الشهر المحدد
    sql = "SELECT SUM(ap.المبلغ_المدفوع) AS إجمالي_الأقساط " & _
          "FROM الأقساط_المدفوعة ap " & _
          "INNER JOIN الطلاب p ON ap.معرف_الطالب = p.معرف_الطالب " & _
          "WHERE p.معرف_المدرس = " & معرف_المدرس & " " & _
          "AND Month(ap.تاريخ_الدفع) = " & الشهر & " " & _
          "AND Year(ap.تاريخ_الدفع) = " & السنة
    
    Set rs = db.OpenRecordset(sql)
    If Not rs.EOF Then
        إجمالي_الأقساط = Nz(rs!إجمالي_الأقساط, 0)
    End If
    rs.Close
    
    ' حساب الراتب حسب النوع
    Select Case نوع_الراتب
        Case "ثابت"
            حساب_راتب_المدرس = الراتب_الثابت
        Case "نسبة"
            حساب_راتب_المدرس = إجمالي_الأقساط * نسبة_المدرس
        Case "مختلط"
            حساب_راتب_المدرس = الراتب_الثابت + (إجمالي_الأقساط * نسبة_المدرس)
        Case Else
            حساب_راتب_المدرس = إجمالي_الأقساط * نسبة_المدرس
    End Select
    
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لتحديث المبلغ المدفوع للطالب
Public Sub تحديث_المبلغ_المدفوع(معرف_الطالب As Long)
    Dim db As Database
    Dim rs As Recordset
    Dim sql As String
    Dim إجمالي_المدفوع As Currency
    
    Set db = CurrentDb()
    
    ' حساب إجمالي المبلغ المدفوع
    sql = "SELECT SUM(المبلغ_المدفوع) AS إجمالي_المدفوع FROM الأقساط_المدفوعة WHERE معرف_الطالب = " & معرف_الطالب
    Set rs = db.OpenRecordset(sql)
    
    If Not rs.EOF Then
        إجمالي_المدفوع = Nz(rs!إجمالي_المدفوع, 0)
    End If
    rs.Close
    
    ' تحديث جدول الطلاب
    sql = "UPDATE الطلاب SET المبلغ_المدفوع = " & إجمالي_المدفوع & " WHERE معرف_الطالب = " & معرف_الطالب
    db.Execute sql
    
    ' حساب وتحديث المبلغ المتبقي
    sql = "UPDATE الطلاب SET المبلغ_المتبقي = رسوم_الدورة - المبلغ_المدفوع WHERE معرف_الطالب = " & معرف_الطالب
    db.Execute sql
    
    Set rs = Nothing
    Set db = Nothing
End Sub

' ===========================================
' Module: إدارة_الطلاب
' ===========================================

' دالة لإنشاء طالب جديد
Public Function إنشاء_طالب_جديد(اسم_الطالب As String, الجنس As String, العمر As Integer, _
                                رقم_الهاتف As String, هاتف_ولي_الامر As String, العنوان As String, _
                                الصف_الدراسي As String, معرف_الدورة As Long, معرف_المدرس As Long, _
                                رسوم_الدورة As Currency, نسبة_الخصم As Double) As Long
    
    Dim db As Database
    Dim rs As Recordset
    Dim رسوم_بعد_الخصم As Currency
    
    Set db = CurrentDb()
    Set rs = db.OpenRecordset("الطلاب", dbOpenDynaset)
    
    ' حساب الرسوم بعد الخصم
    رسوم_بعد_الخصم = رسوم_الدورة - (رسوم_الدورة * نسبة_الخصم)
    
    ' إضافة السجل الجديد
    rs.AddNew
    rs!اسم_الطالب = اسم_الطالب
    rs!الجنس = الجنس
    rs!العمر = العمر
    rs!رقم_الهاتف = رقم_الهاتف
    rs!هاتف_ولي_الامر = هاتف_ولي_الامر
    rs!العنوان = العنوان
    rs!الصف_الدراسي = الصف_الدراسي
    rs!معرف_الدورة = معرف_الدورة
    rs!معرف_المدرس = معرف_المدرس
    rs!رسوم_الدورة = رسوم_بعد_الخصم
    rs!المبلغ_المدفوع = 0
    rs!المبلغ_المتبقي = رسوم_بعد_الخصم
    rs!نسبة_الخصم = نسبة_الخصم
    rs!تاريخ_التسجيل = Now()
    rs!حالة_الطالب = "نشط"
    rs.Update
    
    ' إرجاع معرف الطالب الجديد
    إنشاء_طالب_جديد = rs!معرف_الطالب
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لإنسحاب الطالب
Public Function إنسحاب_الطالب(معرف_الطالب As Long, سبب_الانسحاب As String, _
                              نسبة_الخصم_انسحاب As Double) As Currency
    
    Dim db As Database
    Dim rs As Recordset
    Dim rs_انسحاب As Recordset
    Dim المبلغ_المدفوع As Currency
    Dim المبلغ_المسترد As Currency
    
    Set db = CurrentDb()
    Set rs = db.OpenRecordset("SELECT المبلغ_المدفوع FROM الطلاب WHERE معرف_الطالب = " & معرف_الطالب)
    
    If Not rs.EOF Then
        المبلغ_المدفوع = Nz(rs!المبلغ_المدفوع, 0)
        المبلغ_المسترد = المبلغ_المدفوع - (المبلغ_المدفوع * نسبة_الخصم_انسحاب)
        
        ' تسجيل الانسحاب
        Set rs_انسحاب = db.OpenRecordset("انسحابات_الطلاب", dbOpenDynaset)
        rs_انسحاب.AddNew
        rs_انسحاب!معرف_الطالب = معرف_الطالب
        rs_انسحاب!تاريخ_الانسحاب = Now()
        rs_انسحاب!سبب_الانسحاب = سبب_الانسحاب
        rs_انسحاب!المبلغ_المدفوع_إجمالي = المبلغ_المدفوع
        rs_انسحاب!نسبة_الخصم = نسبة_الخصم_انسحاب
        rs_انسحاب!المبلغ_المسترد = المبلغ_المسترد
        rs_انسحاب!المبلغ_المحتفظ_به = المبلغ_المدفوع - المبلغ_المسترد
        rs_انسحاب.Update
        rs_انسحاب.Close
        
        ' تحديث حالة الطالب
        db.Execute "UPDATE الطلاب SET حالة_الطالب = 'منسحب', تاريخ_الانسحاب = Now(), مبلغ_الاسترداد = " & المبلغ_المسترد & " WHERE معرف_الطالب = " & معرف_الطالب
        
        إنسحاب_الطالب = المبلغ_المسترد
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' ===========================================
' Module: التقارير_والإحصائيات
' ===========================================

' دالة لحساب إجمالي الإيرادات الشهرية
Public Function إجمالي_الإيرادات_الشهرية(الشهر As Integer, السنة As Integer) As Currency
    Dim db As Database
    Dim rs As Recordset
    Dim sql As String
    
    Set db = CurrentDb()
    sql = "SELECT SUM(المبلغ_المدفوع) AS إجمالي_الإيرادات " & _
          "FROM الأقساط_المدفوعة " & _
          "WHERE Month(تاريخ_الدفع) = " & الشهر & " " & _
          "AND Year(تاريخ_الدفع) = " & السنة
    
    Set rs = db.OpenRecordset(sql)
    If Not rs.EOF Then
        إجمالي_الإيرادات_الشهرية = Nz(rs!إجمالي_الإيرادات, 0)
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لحساب إجمالي المصاريف الشهرية
Public Function إجمالي_المصاريف_الشهرية(الشهر As Integer, السنة As Integer) As Currency
    Dim db As Database
    Dim rs As Recordset
    Dim sql As String
    
    Set db = CurrentDb()
    sql = "SELECT SUM(المبلغ) AS إجمالي_المصاريف " & _
          "FROM المصاريف " & _
          "WHERE الشهر = " & الشهر & " " & _
          "AND السنة = " & السنة
    
    Set rs = db.OpenRecordset(sql)
    If Not rs.EOF Then
        إجمالي_المصاريف_الشهرية = Nz(rs!إجمالي_المصاريف, 0)
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لحساب الأرباح الصافية
Public Function الأرباح_الصافية_الشهرية(الشهر As Integer, السنة As Integer) As Currency
    Dim إيرادات As Currency
    Dim مصاريف As Currency
    Dim رواتب_مدرسين As Currency
    Dim db As Database
    Dim rs As Recordset
    Dim sql As String
    
    إيرادات = إجمالي_الإيرادات_الشهرية(الشهر, السنة)
    مصاريف = إجمالي_المصاريف_الشهرية(الشهر, السنة)
    
    ' حساب إجمالي رواتب المدرسين
    Set db = CurrentDb()
    sql = "SELECT SUM(المبلغ_الإجمالي) AS إجمالي_الرواتب " & _
          "FROM رواتب_المدرسين " & _
          "WHERE الشهر = " & الشهر & " " & _
          "AND السنة = " & السنة
    
    Set rs = db.OpenRecordset(sql)
    If Not rs.EOF Then
        رواتب_مدرسين = Nz(rs!إجمالي_الرواتب, 0)
    End If
    
    الأرباح_الصافية_الشهرية = إيرادات - مصاريف - رواتب_مدرسين
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لعدد الطلاب النشطين
Public Function عدد_الطلاب_النشطين() As Long
    Dim db As Database
    Dim rs As Recordset
    
    Set db = CurrentDb()
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS عدد_الطلاب FROM الطلاب WHERE حالة_الطالب = 'نشط'")
    
    If Not rs.EOF Then
        عدد_الطلاب_النشطين = Nz(rs!عدد_الطلاب, 0)
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لعدد المدرسين النشطين
Public Function عدد_المدرسين_النشطين() As Long
    Dim db As Database
    Dim rs As Recordset
    
    Set db = CurrentDb()
    Set rs = db.OpenRecordset("SELECT COUNT(*) AS عدد_المدرسين FROM المدرسين WHERE حالة_المدرس = 'نشط'")
    
    If Not rs.EOF Then
        عدد_المدرسين_النشطين = Nz(rs!عدد_المدرسين, 0)
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function

' دالة لإجمالي الأقساط غير المدفوعة
Public Function إجمالي_الأقساط_غير_المدفوعة() As Currency
    Dim db As Database
    Dim rs As Recordset
    
    Set db = CurrentDb()
    Set rs = db.OpenRecordset("SELECT SUM(المبلغ_المتبقي) AS إجمالي_المتبقي FROM الطلاب WHERE حالة_الطالب = 'نشط' AND المبلغ_المتبقي > 0")
    
    If Not rs.EOF Then
        إجمالي_الأقساط_غير_المدفوعة = Nz(rs!إجمالي_المتبقي, 0)
    End If
    
    rs.Close
    Set rs = Nothing
    Set db = Nothing
End Function