# 📊 مرجع الجداول - هيكل قاعدة البيانات

## 📋 الجداول الـ 8 المطلوبة

### 1️⃣ اعدادات_المعهد
```
المفتاح الأساسي: معرف_الاعداد (AutoNumber)
الحقول:
- اسم_المعهد (Text 100)
- عنوان_المعهد (Text 255)
- رقم_الهاتف (Text 20)
- البريد_الالكتروني (Text 100)
- نسبة_المعهد_افتراضية (Number/Double)
- نسبة_المدرس_افتراضية (Number/Double)
- تاريخ_انشاء_النظام (Date/Time)
- ملاحظات (Long Text)
```

### 2️⃣ الدورات
```
المفتاح الأساسي: معرف_الدورة (AutoNumber)
الحقول:
- اسم_الدورة (Text 100)
- وصف_الدورة (Text 255)
- مدة_الدورة (Number/Integer)
- رسوم_الدورة (Currency)
- حالة_الدورة (Text 20)
- تاريخ_الانشاء (Date/Time)
```

### 3️⃣ المدرسين
```
المفتاح الأساسي: معرف_المدرس (AutoNumber)
الحقول:
- اسم_المدرس (Text 100)
- رقم_الهاتف (Text 20)
- العنوان (Text 255)
- التخصص (Text 100)
- نوع_الراتب (Text 20)
- الراتب_الثابت (Currency)
- نسبة_المدرس (Number/Double)
- تاريخ_التعيين (Date/Time)
- حالة_المدرس (Text 20)
- ملاحظات (Long Text)
```

### 4️⃣ الطلاب
```
المفتاح الأساسي: معرف_الطالب (AutoNumber)
الحقول:
- اسم_الطالب (Text 100)
- الجنس (Text 10)
- العمر (Number/Integer)
- رقم_الهاتف (Text 20)
- هاتف_ولي_الامر (Text 20)
- العنوان (Text 255)
- الصف_الدراسي (Text 50)
- معرف_الدورة (Number/Long Integer) [FK]
- معرف_المدرس (Number/Long Integer) [FK]
- رسوم_الدورة (Currency)
- المبلغ_المدفوع (Currency)
- المبلغ_المتبقي (Currency)
- نسبة_الخصم (Number/Double)
- تاريخ_التسجيل (Date/Time)
- حالة_الطالب (Text 20)
- تاريخ_الانسحاب (Date/Time)
- مبلغ_الاسترداد (Currency)
- ملاحظات (Long Text)
```

### 5️⃣ الأقساط_المدفوعة
```
المفتاح الأساسي: معرف_الدفع (AutoNumber)
الحقول:
- معرف_الطالب (Number/Long Integer) [FK]
- المبلغ_المدفوع (Currency)
- تاريخ_الدفع (Date/Time)
- طريقة_الدفع (Text 20)
- رقم_الإيصال (Text 20)
- الملاحظات (Text 255)
```

### 6️⃣ المصاريف
```
المفتاح الأساسي: معرف_المصروف (AutoNumber)
الحقول:
- نوع_المصروف (Text 100)
- المبلغ (Currency)
- تاريخ_المصروف (Date/Time)
- وصف_المصروف (Text 255)
- فئة_المصروف (Text 50)
- الشهر (Number/Integer)
- السنة (Number/Integer)
- ملاحظات (Long Text)
```

### 7️⃣ رواتب_المدرسين
```
المفتاح الأساسي: معرف_الراتب (AutoNumber)
الحقول:
- معرف_المدرس (Number/Long Integer) [FK]
- الشهر (Number/Integer)
- السنة (Number/Integer)
- عدد_الطلاب (Number/Integer)
- إجمالي_الأقساط (Currency)
- نسبة_المدرس (Number/Double)
- مبلغ_الراتب (Currency)
- الراتب_الثابت (Currency)
- المبلغ_الإجمالي (Currency)
- تاريخ_الدفع (Date/Time)
- حالة_الدفع (Text 20)
- ملاحظات (Text 255)
```

### 8️⃣ انسحابات_الطلاب
```
المفتاح الأساسي: معرف_الانسحاب (AutoNumber)
الحقول:
- معرف_الطالب (Number/Long Integer) [FK]
- تاريخ_الانسحاب (Date/Time)
- سبب_الانسحاب (Text 255)
- المبلغ_المدفوع_إجمالي (Currency)
- نسبة_الخصم (Number/Double)
- المبلغ_المسترد (Currency)
- المبلغ_المحتفظ_به (Currency)
- ملاحظات (Long Text)
```

---

## 🔗 العلاقات بين الجداول

### العلاقة 1: الدورات ← الطلاب
```
الدورات.معرف_الدورة → الطلاب.معرف_الدورة
نوع: One-to-Many (واحد لمتعدد)
```

### العلاقة 2: المدرسين ← الطلاب
```
المدرسين.معرف_المدرس → الطلاب.معرف_المدرس
نوع: One-to-Many (واحد لمتعدد)
```

### العلاقة 3: الطلاب ← الأقساط المدفوعة
```
الطلاب.معرف_الطالب → الأقساط_المدفوعة.معرف_الطالب
نوع: One-to-Many (واحد لمتعدد)
```

### العلاقة 4: المدرسين ← رواتب المدرسين
```
المدرسين.معرف_المدرس → رواتب_المدرسين.معرف_المدرس
نوع: One-to-Many (واحد لمتعدد)
```

### العلاقة 5: الطلاب ← انسحابات الطلاب
```
الطلاب.معرف_الطالب → انسحابات_الطلاب.معرف_الطالب
نوع: One-to-Many (واحد لمتعدد)
```

---

## 🎯 إنشاء العلاقات في Access

### خطوات إنشاء العلاقات:
```
1. Database Tools → Relationships
2. Add Table → أضف جميع الجداول
3. اسحب من الحقل الأساسي إلى الحقل الأجنبي
4. في نافذة Edit Relationships:
   - ✅ Enforce Referential Integrity
   - ✅ Cascade Update Related Fields
   - ✅ Cascade Delete Related Records (حذر!)
5. اضغط Create
```

---

## 📝 القيم الافتراضية المهمة

### للطلاب:
- `تاريخ_التسجيل`: `=Now()`
- `حالة_الطالب`: `"نشط"`
- `المبلغ_المدفوع`: `0`
- `نسبة_الخصم`: `0`

### للمدرسين:
- `تاريخ_التعيين`: `=Now()`
- `حالة_المدرس`: `"نشط"`
- `نوع_الراتب`: `"نسبة"`
- `نسبة_المدرس`: `0.3`

### للأقساط:
- `تاريخ_الدفع`: `=Now()`
- `طريقة_الدفع`: `"نقد"`

### للمصاريف:
- `تاريخ_المصروف`: `=Now()`
- `الشهر`: `=Month(Now())`
- `السنة`: `=Year(Now())`

---

## 🔍 فهارس الأداء

### الفهارس المطلوبة:
```
الطلاب:
- idx_اسم_الطالب ON اسم_الطالب
- idx_حالة_الطالب ON حالة_الطالب

المدرسين:
- idx_اسم_المدرس ON اسم_المدرس
- idx_حالة_المدرس ON حالة_المدرس

الأقساط المدفوعة:
- idx_تاريخ_الدفع ON تاريخ_الدفع

المصاريف:
- idx_تاريخ_المصروف ON تاريخ_المصروف

رواتب المدرسين:
- idx_شهر_سنة ON (الشهر, السنة)
```

---

## 📊 حجم البيانات المتوقع

### البيانات التجريبية:
- 📋 **الطلاب**: 16 سجل
- 👥 **المدرسين**: 8 سجل  
- 📚 **الدورات**: 8 سجل
- 💰 **الأقساط**: 22 سجل
- 💸 **المصاريف**: 11 سجل
- 💼 **الرواتب**: 16 سجل

### الاستخدام العملي (تقدير):
- 📋 **الطلاب**: 200-500 سجل
- 👥 **المدرسين**: 20-50 سجل
- 📚 **الدورات**: 15-30 سجل
- 💰 **الأقساط**: 1000-5000 سجل/سنة
- 💸 **المصاريف**: 200-500 سجل/سنة
- 💼 **الرواتب**: 240-600 سجل/سنة

---

## 🚀 تحسين الأداء

### نصائح مهمة:
- ✅ استخدم الفهارس على الحقول المبحوث عنها كثيراً
- ✅ تجنب الحقول الكبيرة (Long Text) في الاستعلامات
- ✅ استخدم المعايير (Criteria) لتقليل النتائج
- ✅ اعمل ضغط وإصلاح دوري للقاعدة

### مراقبة الأداء:
- 📊 حجم قاعدة البيانات (يجب أن تكون < 2GB)
- ⚡ سرعة الاستعلامات (يجب أن تكون < 3 ثوان)
- 💾 استخدام الذاكرة (يجب أن يكون معقول)

---

## 🔧 استكشاف الأخطاء

### خطأ شائع: "Cannot add or change a record"
**السبب:** مشكلة في المفاتيح الأجنبية
**الحل:** تأكد من وجود السجل المرتبط أولاً

### خطأ شائع: "Field name not found"
**السبب:** خطأ في اسم الحقل
**الحل:** تأكد من الأسماء العربية الصحيحة

### خطأ شائع: "Data type mismatch"
**السبب:** نوع البيانات غير متطابق
**الحل:** تأكد من أنواع البيانات في الجدولين

---

## 📋 قائمة تحقق الجداول

### ✅ قبل إنشاء البيانات:
- [ ] تم إنشاء جميع الجداول الـ 8
- [ ] تم تعيين المفاتيح الأساسية
- [ ] تم إنشاء العلاقات بين الجداول
- [ ] تم اختبار العلاقات

### ✅ بعد إضافة البيانات:
- [ ] تم إضافة البيانات التجريبية
- [ ] تم اختبار الاستعلامات
- [ ] تم التحقق من سلامة البيانات
- [ ] تم إنشاء الفهارس

---

## 🎯 الخطوة التالية

بعد إنشاء الجداول بنجاح، انتقل إلى:
1. **إضافة البيانات** من `sample_data.sql`
2. **إنشاء الاستعلامات** من `Additional_Reports_Queries.xml`
3. **إنشاء النماذج** من `Forms_Design.xml`

---

**مرجع سريع للمطورين والمستخدمين المتقدمين** 🚀