-- نظام محاسبة المعاهد الأهلية العراقية
-- Iraqi Private Institutes Accounting System
-- تصميم قاعدة البيانات - Database Design

-- جدول إعدادات المعهد
CREATE TABLE InstituteSettings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    InstituteName NVARCHAR(255) NOT NULL,
    InstituteAddress NVARCHAR(500),
    PhoneNumber NVARCHAR(50),
    InstituteSharePercentage DECIMAL(5,2) DEFAULT 70.00,
    LogoPath NVARCHAR(255),
    CreatedDate DATETIME DEFAULT NOW(),
    ModifiedDate DATETIME DEFAULT NOW()
);

-- جدول الدورات
CREATE TABLE Courses (
    CourseID AUTOINCREMENT PRIMARY KEY,
    CourseName NVARCHAR(255) NOT NULL,
    CourseDescription NVARCHAR(500),
    DefaultFee CURRENCY DEFAULT 0,
    Duration NVARCHAR(100),
    IsActive BIT DEFAULT TRUE,
    CreatedDate DATETIME DEFAULT NOW()
);

-- جدول المدرسين
CREATE TABLE Teachers (
    TeacherID AUTOINCREMENT PRIMARY KEY,
    TeacherName NVARCHAR(255) NOT NULL,
    Specialization NVARCHAR(255),
    PhoneNumber NVARCHAR(50),
    SalaryType NVARCHAR(50) DEFAULT 'نسبة', -- 'ثابت' أو 'نسبة'
    MonthlySalary CURRENCY DEFAULT 0,
    SharePercentage DECIMAL(5,2) DEFAULT 30.00,
    HireDate DATETIME DEFAULT NOW(),
    IsActive BIT DEFAULT TRUE,
    Notes MEMO
);

-- جدول الطلاب
CREATE TABLE Students (
    StudentID AUTOINCREMENT PRIMARY KEY,
    StudentName NVARCHAR(255) NOT NULL,
    Gender NVARCHAR(10) DEFAULT 'ذكر', -- 'ذكر' أو 'أنثى'
    Age INTEGER,
    Grade NVARCHAR(50),
    CourseID INTEGER,
    TeacherID INTEGER,
    TotalFee CURRENCY DEFAULT 0,
    PaidAmount CURRENCY DEFAULT 0,
    RemainingAmount CURRENCY DEFAULT 0,
    RegistrationDate DATETIME DEFAULT NOW(),
    StudentStatus NVARCHAR(50) DEFAULT 'نشط', -- 'نشط' أو 'منسحب'
    WithdrawalDate DATETIME,
    RefundAmount CURRENCY DEFAULT 0,
    ParentName NVARCHAR(255),
    ParentPhone NVARCHAR(50),
    Address NVARCHAR(500),
    Notes MEMO,
    FOREIGN KEY (CourseID) REFERENCES Courses(CourseID),
    FOREIGN KEY (TeacherID) REFERENCES Teachers(TeacherID)
);

-- جدول الدفعات
CREATE TABLE Payments (
    PaymentID AUTOINCREMENT PRIMARY KEY,
    StudentID INTEGER NOT NULL,
    AmountPaid CURRENCY NOT NULL,
    PaymentDate DATETIME DEFAULT NOW(),
    PaymentMethod NVARCHAR(50) DEFAULT 'نقدي', -- 'نقدي' أو 'تحويل' أو 'شيك'
    ReceiptNumber NVARCHAR(100),
    Notes MEMO,
    CreatedBy NVARCHAR(100),
    FOREIGN KEY (StudentID) REFERENCES Students(StudentID)
);

-- جدول المصاريف
CREATE TABLE Expenses (
    ExpenseID AUTOINCREMENT PRIMARY KEY,
    ExpenseType NVARCHAR(255) NOT NULL,
    Amount CURRENCY NOT NULL,
    ExpenseDate DATETIME DEFAULT NOW(),
    Description NVARCHAR(500),
    ExpenseMonth INTEGER,
    ExpenseYear INTEGER,
    IsRecurring BIT DEFAULT FALSE,
    Category NVARCHAR(100), -- 'إيجار', 'كهرباء', 'طباعة', 'أخرى'
    Notes MEMO
);

-- جدول أرباح المدرسين
CREATE TABLE TeacherEarnings (
    EarningID AUTOINCREMENT PRIMARY KEY,
    TeacherID INTEGER NOT NULL,
    Month INTEGER NOT NULL,
    Year INTEGER NOT NULL,
    StudentCount INTEGER DEFAULT 0,
    TotalStudentFees CURRENCY DEFAULT 0,
    TeacherShare CURRENCY DEFAULT 0,
    InstituteShare CURRENCY DEFAULT 0,
    IsPaid BIT DEFAULT FALSE,
    PaymentDate DATETIME,
    Notes MEMO,
    FOREIGN KEY (TeacherID) REFERENCES Teachers(TeacherID)
);

-- جدول الانسحابات
CREATE TABLE Withdrawals (
    WithdrawalID AUTOINCREMENT PRIMARY KEY,
    StudentID INTEGER NOT NULL,
    WithdrawalDate DATETIME DEFAULT NOW(),
    Reason NVARCHAR(500),
    RefundAmount CURRENCY DEFAULT 0,
    RefundPercentage DECIMAL(5,2) DEFAULT 0,
    ProcessedBy NVARCHAR(100),
    Notes MEMO,
    FOREIGN KEY (StudentID) REFERENCES Students(StudentID)
);

-- إدراج البيانات الأولية

-- إعدادات المعهد الافتراضية
INSERT INTO InstituteSettings (InstituteName, InstituteAddress, PhoneNumber, InstituteSharePercentage)
VALUES ('معهد النور الأهلي', 'بغداد - الكرادة', '07901234567', 70.00);

-- دورات افتراضية
INSERT INTO Courses (CourseName, CourseDescription, DefaultFee, Duration)
VALUES 
('دورة اللغة الإنجليزية - المستوى الأول', 'دورة تأسيسية في اللغة الإنجليزية', 150000, '3 أشهر'),
('دورة اللغة الإنجليزية - المستوى الثاني', 'دورة متوسطة في اللغة الإنجليزية', 200000, '3 أشهر'),
('دورة الرياضيات - الصف السادس', 'دورة تقوية في الرياضيات للصف السادس', 120000, 'شهرين'),
('دورة الفيزياء - الصف الثالث متوسط', 'دورة تقوية في الفيزياء', 180000, '3 أشهر'),
('دورة الكيمياء - الصف السادس علمي', 'دورة تحضيرية للامتحانات الوزارية', 250000, '4 أشهر');

-- مدرسين افتراضيين
INSERT INTO Teachers (TeacherName, Specialization, PhoneNumber, SalaryType, SharePercentage)
VALUES 
('أ. أحمد محمد علي', 'اللغة الإنجليزية', '07701234567', 'نسبة', 30.00),
('أ. فاطمة حسن', 'الرياضيات', '07801234567', 'نسبة', 35.00),
('د. علي جاسم', 'الفيزياء', '07901234567', 'ثابت', 25.00),
('أ. زينب كريم', 'الكيمياء', '07601234567', 'نسبة', 30.00);
