<?xml version="1.0" encoding="UTF-8"?>
<!-- التقارير والاستعلامات الإضافية - نظام محاسبة المعاهد الأهلية العراقية -->
<AdditionalReportsQueries>
    
    <!-- =============================================== -->
    <!-- تقرير إيصال الدفع -->
    <!-- =============================================== -->
    <Report name="تقرير_إيصال_الدفع">
        <Properties>
            <Caption>إيصال دفع قسط</Caption>
            <RecordSource>
                SELECT 
                    ap.معرف_الدفع,
                    ap.تاريخ_الدفع,
                    ap.المبلغ_المدفوع,
                    ap.طريقة_الدفع,
                    ap.رقم_الإيصال,
                    ap.الملاحظات,
                    p.اسم_الطالب,
                    p.رقم_الهاتف,
                    p.هاتف_ولي_الامر,
                    p.الصف_الدراسي,
                    c.اسم_الدورة,
                    c.رسوم_الدورة,
                    p.المبلغ_المدفوع AS إجمالي_المدفوع,
                    p.المبلغ_المتبقي,
                    t.اسم_المدرس,
                    inst.اسم_المعهد,
                    inst.عنوان_المعهد,
                    inst.رقم_الهاتف AS هاتف_المعهد
                FROM ((((الأقساط_المدفوعة ap
                INNER JOIN الطلاب p ON ap.معرف_الطالب = p.معرف_الطالب)
                LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
                LEFT JOIN المدرسين t ON p.معرف_المدرس = t.معرف_المدرس)
                CROSS JOIN اعدادات_المعهد inst)
            </RecordSource>
            <Width>8.5</Width>
            <Height>5.5</Height>
            <RightToLeft>True</RightToLeft>
        </Properties>
        
        <Sections>
            <!-- رأس التقرير -->
            <ReportHeader>
                <Properties>
                    <Height>2</Height>
                    <BackColor>#F8F9FA</BackColor>
                </Properties>
                <Controls>
                    <!-- شعار المعهد -->
                    <Rectangle name="rect_شعار">
                        <Properties>
                            <Left>6.5</Left>
                            <Top>0.1</Top>
                            <Width>1.5</Width>
                            <Height>1.5</Height>
                            <BackColor>#E8F4FD</BackColor>
                            <BorderColor>#3498DB</BorderColor>
                        </Properties>
                    </Rectangle>
                    
                    <!-- معلومات المعهد -->
                    <Label name="lbl_اسم_المعهد_إيصال">
                        <Properties>
                            <Left>3</Left>
                            <Top>0.2</Top>
                            <Width>3</Width>
                            <Height>0.4</Height>
                            <ControlSource>اسم_المعهد</ControlSource>
                            <FontSize>18</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <ForeColor>#2C3E50</ForeColor>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_عنوان_المعهد_إيصال">
                        <Properties>
                            <Left>3</Left>
                            <Top>0.7</Top>
                            <Width>3</Width>
                            <Height>0.3</Height>
                            <ControlSource>عنوان_المعهد</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Center</TextAlign>
                            <ForeColor>#34495E</ForeColor>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_هاتف_المعهد_إيصال">
                        <Properties>
                            <Left>3</Left>
                            <Top>1.1</Top>
                            <Width>3</Width>
                            <Height>0.3</Height>
                            <ControlSource>="هاتف: " & [هاتف_المعهد]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Center</TextAlign>
                            <ForeColor>#34495E</ForeColor>
                        </Properties>
                    </Label>
                    
                    <!-- عنوان الإيصال -->
                    <Label name="lbl_عنوان_الإيصال">
                        <Properties>
                            <Left>3</Left>
                            <Top>1.5</Top>
                            <Width>3</Width>
                            <Height>0.4</Height>
                            <Caption>إيصال دفع قسط</Caption>
                            <FontSize>16</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Center</TextAlign>
                            <ForeColor>#E74C3C</ForeColor>
                            <BorderStyle>Solid</BorderStyle>
                        </Properties>
                    </Label>
                    
                    <!-- رقم الإيصال والتاريخ -->
                    <Label name="lbl_رقم_الإيصال_عنوان">
                        <Properties>
                            <Left>0.5</Left>
                            <Top>0.2</Top>
                            <Width>1.5</Width>
                            <Height>0.3</Height>
                            <ControlSource>="رقم الإيصال: " & [رقم_الإيصال]</ControlSource>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Left</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_تاريخ_الإيصال">
                        <Properties>
                            <Left>0.5</Left>
                            <Top>0.6</Top>
                            <Width>1.5</Width>
                            <Height>0.3</Height>
                            <ControlSource>="التاريخ: " & Format([تاريخ_الدفع],"dd/mm/yyyy")</ControlSource>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Left</TextAlign>
                        </Properties>
                    </Label>
                </Controls>
            </ReportHeader>
            
            <!-- تفاصيل الإيصال -->
            <Detail>
                <Properties>
                    <Height>2.5</Height>
                    <CanGrow>True</CanGrow>
                </Properties>
                <Controls>
                    <!-- معلومات الطالب -->
                    <Rectangle name="rect_معلومات_الطالب_إيصال">
                        <Properties>
                            <Left>0.5</Left>
                            <Top>0.1</Top>
                            <Width>7.5</Width>
                            <Height>1.2</Height>
                            <BackColor>#FFFFFF</BackColor>
                            <BorderColor>#BDC3C7</BorderColor>
                            <SpecialEffect>Sunken</SpecialEffect>
                        </Properties>
                    </Rectangle>
                    
                    <Label name="lbl_بيانات_الطالب">
                        <Properties>
                            <Left>7</Left>
                            <Top>0.2</Top>
                            <Width>0.8</Width>
                            <Height>0.3</Height>
                            <Caption>بيانات الطالب:</Caption>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_اسم_الطالب_إيصال">
                        <Properties>
                            <Left>5</Left>
                            <Top>0.2</Top>
                            <Width>1.8</Width>
                            <Height>0.3</Height>
                            <ControlSource>="الاسم: " & [اسم_الطالب]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_الصف_الدراسي_إيصال">
                        <Properties>
                            <Left>2.5</Left>
                            <Top>0.2</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="الصف: " & [الصف_الدراسي]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_الدورة_إيصال">
                        <Properties>
                            <Left>5</Left>
                            <Top>0.6</Top>
                            <Width>2.8</Width>
                            <Height>0.3</Height>
                            <ControlSource>="الدورة: " & [اسم_الدورة]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_المدرس_إيصال">
                        <Properties>
                            <Left>2.5</Left>
                            <Top>0.6</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="المدرس: " & [اسم_المدرس]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_هاتف_الطالب_إيصال">
                        <Properties>
                            <Left>5</Left>
                            <Top>1</Top>
                            <Width>2.8</Width>
                            <Height>0.3</Height>
                            <ControlSource>="الهاتف: " & [رقم_الهاتف]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <!-- تفاصيل الدفع -->
                    <Rectangle name="rect_تفاصيل_الدفع">
                        <Properties>
                            <Left>0.5</Left>
                            <Top>1.4</Top>
                            <Width>7.5</Width>
                            <Height>1</Height>
                            <BackColor>#E8F6F0</BackColor>
                            <BorderColor>#27AE60</BorderColor>
                            <SpecialEffect>Sunken</SpecialEffect>
                        </Properties>
                    </Rectangle>
                    
                    <Label name="lbl_تفاصيل_الدفع">
                        <Properties>
                            <Left>7</Left>
                            <Top>1.5</Top>
                            <Width>0.8</Width>
                            <Height>0.3</Height>
                            <Caption>تفاصيل الدفع:</Caption>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_رسوم_الدورة_إيصال">
                        <Properties>
                            <Left>5.5</Left>
                            <Top>1.8</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="رسوم الدورة: " & Format([رسوم_الدورة],"Currency")</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_المبلغ_المدفوع_إيصال">
                        <Properties>
                            <Left>3.5</Left>
                            <Top>1.8</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="المبلغ المدفوع: " & Format([المبلغ_المدفوع],"Currency")</ControlSource>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Right</TextAlign>
                            <ForeColor>#E74C3C</ForeColor>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_طريقة_الدفع_إيصال">
                        <Properties>
                            <Left>1.5</Left>
                            <Top>1.8</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="طريقة الدفع: " & [طريقة_الدفع]</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_إجمالي_المدفوع_إيصال">
                        <Properties>
                            <Left>5.5</Left>
                            <Top>2.1</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="إجمالي المدفوع: " & Format([إجمالي_المدفوع],"Currency")</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_المبلغ_المتبقي_إيصال">
                        <Properties>
                            <Left>3.5</Left>
                            <Top>2.1</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <ControlSource>="المبلغ المتبقي: " & Format([المبلغ_المتبقي],"Currency")</ControlSource>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Right</TextAlign>
                            <ForeColor>#F39C12</ForeColor>
                        </Properties>
                    </Label>
                </Controls>
            </Detail>
            
            <!-- ذيل التقرير -->
            <ReportFooter>
                <Properties>
                    <Height>1.5</Height>
                    <BackColor>#F8F9FA</BackColor>
                </Properties>
                <Controls>
                    <!-- خط فاصل -->
                    <Line name="line_فاصل">
                        <Properties>
                            <Left>0.5</Left>
                            <Top>0.1</Top>
                            <Width>7.5</Width>
                            <Height>0</Height>
                            <BorderColor>#BDC3C7</BorderColor>
                            <BorderWidth>2</BorderWidth>
                        </Properties>
                    </Line>
                    
                    <!-- المبلغ بالكلمات -->
                    <Label name="lbl_المبلغ_بالكلمات">
                        <Properties>
                            <Left>7</Left>
                            <Top>0.3</Top>
                            <Width>1</Width>
                            <Height>0.3</Height>
                            <Caption>المبلغ بالكلمات:</Caption>
                            <FontSize>12</FontSize>
                            <FontWeight>Bold</FontWeight>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Label name="lbl_المبلغ_نصاً">
                        <Properties>
                            <Left>2</Left>
                            <Top>0.3</Top>
                            <Width>4.8</Width>
                            <Height>0.3</Height>
                            <ControlSource>=رقم_إلى_كلمات([المبلغ_المدفوع]) & " دينار عراقي"</ControlSource>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                            <BorderStyle>Solid</BorderStyle>
                        </Properties>
                    </Label>
                    
                    <!-- توقيع المحاسب -->
                    <Label name="lbl_توقيع_المحاسب">
                        <Properties>
                            <Left>1</Left>
                            <Top>0.8</Top>
                            <Width>1.5</Width>
                            <Height>0.3</Height>
                            <Caption>توقيع المحاسب:</Caption>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Rectangle name="rect_توقيع">
                        <Properties>
                            <Left>0.5</Left>
                            <Top>1.2</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <BackColor>#FFFFFF</BackColor>
                            <BorderColor>#BDC3C7</BorderColor>
                        </Properties>
                    </Rectangle>
                    
                    <!-- ختم المعهد -->
                    <Label name="lbl_ختم_المعهد">
                        <Properties>
                            <Left>6</Left>
                            <Top>0.8</Top>
                            <Width>1.5</Width>
                            <Height>0.3</Height>
                            <Caption>ختم المعهد:</Caption>
                            <FontSize>12</FontSize>
                            <TextAlign>Right</TextAlign>
                        </Properties>
                    </Label>
                    
                    <Rectangle name="rect_ختم">
                        <Properties>
                            <Left>5.5</Left>
                            <Top>1.2</Top>
                            <Width>2</Width>
                            <Height>0.3</Height>
                            <BackColor>#FFFFFF</BackColor>
                            <BorderColor>#BDC3C7</BorderColor>
                        </Properties>
                    </Rectangle>
                </Controls>
            </ReportFooter>
        </Sections>
    </Report>
    
    <!-- =============================================== -->
    <!-- الاستعلامات الإضافية -->
    <!-- =============================================== -->
    <Queries>
        
        <!-- استعلام الطلاب المفصل -->
        <Query name="استعلام_الطلاب">
            <SQL>
                SELECT 
                    p.معرف_الطالب,
                    p.اسم_الطالب,
                    p.الجنس,
                    p.العمر,
                    p.رقم_الهاتف,
                    p.هاتف_ولي_الامر,
                    p.العنوان,
                    p.الصف_الدراسي,
                    c.اسم_الدورة,
                    c.رسوم_الدورة AS رسوم_الدورة_الأصلية,
                    t.اسم_المدرس,
                    t.التخصص,
                    p.رسوم_الدورة,
                    p.المبلغ_المدفوع,
                    p.المبلغ_المتبقي,
                    p.نسبة_الخصم,
                    p.تاريخ_التسجيل,
                    p.حالة_الطالب,
                    p.تاريخ_الانسحاب,
                    p.مبلغ_الاسترداد,
                    IIf([المبلغ_المتبقي] > 0, "مستحق", "مكتمل") AS حالة_الدفع
                FROM ((الطلاب p 
                LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
                LEFT JOIN المدرسين t ON p.معرف_المدرس = t.معرف_المدرس)
                ORDER BY p.حالة_الطالب, p.اسم_الطالب
            </SQL>
        </Query>
        
        <!-- استعلام المدرسين المفصل -->
        <Query name="استعلام_المدرسين">
            <SQL>
                SELECT 
                    t.معرف_المدرس,
                    t.اسم_المدرس,
                    t.رقم_الهاتف,
                    t.العنوان,
                    t.التخصص,
                    t.نوع_الراتب,
                    t.الراتب_الثابت,
                    t.نسبة_المدرس,
                    t.تاريخ_التعيين,
                    t.حالة_المدرس,
                    COUNT(p.معرف_الطالب) AS عدد_الطلاب_الحالي,
                    SUM(p.رسوم_الدورة) AS إجمالي_رسوم_الطلاب,
                    SUM(p.المبلغ_المدفوع) AS إجمالي_المبلغ_المدفوع,
                    SUM(p.المبلغ_المتبقي) AS إجمالي_المبلغ_المتبقي,
                    IIf([نوع_الراتب] = "ثابت", [الراتب_الثابت], 
                        IIf([نوع_الراتب] = "نسبة", SUM([p].[المبلغ_المدفوع]) * [نسبة_المدرس], 
                            [الراتب_الثابت] + (SUM([p].[المبلغ_المدفوع]) * [نسبة_المدرس]))) AS الراتب_المتوقع
                FROM المدرسين t
                LEFT JOIN الطلاب p ON t.معرف_المدرس = p.معرف_المدرس AND p.حالة_الطالب = 'نشط'
                GROUP BY t.معرف_المدرس, t.اسم_المدرس, t.رقم_الهاتف, t.العنوان, t.التخصص, 
                         t.نوع_الراتب, t.الراتب_الثابت, t.نسبة_المدرس, t.تاريخ_التعيين, t.حالة_المدرس
                ORDER BY t.حالة_المدرس, t.اسم_المدرس
            </SQL>
        </Query>
        
        <!-- استعلام الأقساط المستحقة -->
        <Query name="استعلام_الأقساط_المستحقة">
            <SQL>
                SELECT 
                    p.معرف_الطالب,
                    p.اسم_الطالب,
                    p.رقم_الهاتف,
                    p.هاتف_ولي_الامر,
                    c.اسم_الدورة,
                    t.اسم_المدرس,
                    p.رسوم_الدورة,
                    p.المبلغ_المدفوع,
                    p.المبلغ_المتبقي,
                    p.تاريخ_التسجيل,
                    DateDiff("d", [تاريخ_التسجيل], Now()) AS أيام_التسجيل,
                    IIf(DateDiff("d", [تاريخ_التسجيل], Now()) > 30, "متأخر", "مستحق") AS حالة_الاستحقاق
                FROM ((الطلاب p 
                LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
                LEFT JOIN المدرسين t ON p.معرف_المدرس = t.معرف_المدرس)
                WHERE p.حالة_الطالب = 'نشط' AND p.المبلغ_المتبقي > 0
                ORDER BY p.المبلغ_المتبقي DESC, p.تاريخ_التسجيل
            </SQL>
        </Query>
        
        <!-- استعلام الأقساط المدفوعة اليوم -->
        <Query name="استعلام_الأقساط_المدفوعة_اليوم">
            <SQL>
                SELECT 
                    ap.معرف_الدفع,
                    ap.تاريخ_الدفع,
                    ap.المبلغ_المدفوع,
                    ap.طريقة_الدفع,
                    ap.رقم_الإيصال,
                    p.اسم_الطالب,
                    c.اسم_الدورة,
                    t.اسم_المدرس,
                    ap.الملاحظات
                FROM (((الأقساط_المدفوعة ap
                INNER JOIN الطلاب p ON ap.معرف_الطالب = p.معرف_الطالب)
                LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
                LEFT JOIN المدرسين t ON p.معرف_المدرس = t.معرف_المدرس)
                WHERE DateValue([تاريخ_الدفع]) = DateValue(Date())
                ORDER BY ap.تاريخ_الدفع DESC
            </SQL>
        </Query>
        
        <!-- استعلام الأقساط المدفوعة الشهرية -->
        <Query name="استعلام_الأقساط_المدفوعة_الشهرية">
            <SQL>
                SELECT 
                    Format([تاريخ_الدفع], "yyyy-mm") AS الشهر,
                    COUNT(ap.معرف_الدفع) AS عدد_الدفعات,
                    SUM(ap.المبلغ_المدفوع) AS إجمالي_المبلغ,
                    AVG(ap.المبلغ_المدفوع) AS متوسط_الدفعة,
                    MIN(ap.المبلغ_المدفوع) AS أقل_دفعة,
                    MAX(ap.المبلغ_المدفوع) AS أعلى_دفعة
                FROM الأقساط_المدفوعة ap
                GROUP BY Format([تاريخ_الدفع], "yyyy-mm")
                ORDER BY الشهر DESC
            </SQL>
        </Query>
        
        <!-- استعلام المصاريف الشهرية -->
        <Query name="استعلام_المصاريف_الشهرية">
            <SQL>
                SELECT 
                    [السنة] & "-" & Format([الشهر], "00") AS الشهر,
                    e.فئة_المصروف,
                    COUNT(e.معرف_المصروف) AS عدد_المصاريف,
                    SUM(e.المبلغ) AS إجمالي_المبلغ,
                    AVG(e.المبلغ) AS متوسط_المصروف
                FROM المصاريف e
                GROUP BY [السنة] & "-" & Format([الشهر], "00"), e.فئة_المصروف
                ORDER BY الشهر DESC, e.فئة_المصروف
            </SQL>
        </Query>
        
        <!-- استعلام الإحصائيات المالية الشهرية -->
        <Query name="استعلام_الإحصائيات_المالية_الشهرية">
            <SQL>
                SELECT 
                    [السنة] & "-" & Format([الشهر], "00") AS الشهر,
                    SUM(IIf([النوع] = "إيراد", [المبلغ], 0)) AS إجمالي_الإيرادات,
                    SUM(IIf([النوع] = "مصروف", [المبلغ], 0)) AS إجمالي_المصاريف,
                    SUM(IIf([النوع] = "راتب", [المبلغ], 0)) AS إجمالي_الرواتب,
                    (SUM(IIf([النوع] = "إيراد", [المبلغ], 0)) - 
                     SUM(IIf([النوع] = "مصروف", [المبلغ], 0)) - 
                     SUM(IIf([النوع] = "راتب", [المبلغ], 0))) AS صافي_الربح
                FROM (
                    SELECT 
                        Month([تاريخ_الدفع]) AS الشهر,
                        Year([تاريخ_الدفع]) AS السنة,
                        [المبلغ_المدفوع] AS المبلغ,
                        "إيراد" AS النوع
                    FROM الأقساط_المدفوعة
                    
                    UNION ALL
                    
                    SELECT 
                        [الشهر],
                        [السنة],
                        [المبلغ],
                        "مصروف" AS النوع
                    FROM المصاريف
                    
                    UNION ALL
                    
                    SELECT 
                        [الشهر],
                        [السنة],
                        [المبلغ_الإجمالي],
                        "راتب" AS النوع
                    FROM رواتب_المدرسين
                    WHERE [حالة_الدفع] = 'مدفوع'
                ) AS البيانات_المالية
                GROUP BY [السنة] & "-" & Format([الشهر], "00")
                ORDER BY الشهر DESC
            </SQL>
        </Query>
        
        <!-- استعلام انسحابات الطلاب -->
        <Query name="استعلام_انسحابات_الطلاب">
            <SQL>
                SELECT 
                    w.معرف_الانسحاب,
                    w.تاريخ_الانسحاب,
                    p.اسم_الطالب,
                    c.اسم_الدورة,
                    t.اسم_المدرس,
                    w.سبب_الانسحاب,
                    w.المبلغ_المدفوع_إجمالي,
                    w.نسبة_الخصم,
                    w.المبلغ_المسترد,
                    w.المبلغ_المحتفظ_به,
                    w.ملاحظات
                FROM (((انسحابات_الطلاب w
                INNER JOIN الطلاب p ON w.معرف_الطالب = p.معرف_الطالب)
                LEFT JOIN الدورات c ON p.معرف_الدورة = c.معرف_الدورة)
                LEFT JOIN المدرسين t ON p.معرف_المدرس = t.معرف_المدرس)
                ORDER BY w.تاريخ_الانسحاب DESC
            </SQL>
        </Query>
        
        <!-- استعلام أداء المدرسين -->
        <Query name="استعلام_أداء_المدرسين">
            <SQL>
                SELECT 
                    t.معرف_المدرس,
                    t.اسم_المدرس,
                    t.التخصص,
                    COUNT(p.معرف_الطالب) AS عدد_الطلاب_الإجمالي,
                    SUM(IIf([p].[حالة_الطالب] = "نشط", 1, 0)) AS عدد_الطلاب_النشطين,
                    SUM(IIf([p].[حالة_الطالب] = "منسحب", 1, 0)) AS عدد_الطلاب_المنسحبين,
                    SUM(IIf([p].[حالة_الطالب] = "متخرج", 1, 0)) AS عدد_الطلاب_المتخرجين,
                    SUM(p.المبلغ_المدفوع) AS إجمالي_الأقساط_المدفوعة,
                    SUM(p.المبلغ_المتبقي) AS إجمالي_الأقساط_المتبقية,
                    Round(SUM([p].[المبلغ_المدفوع]) / SUM([p].[رسوم_الدورة]) * 100, 2) AS نسبة_التحصيل,
                    Round(SUM(IIf([p].[حالة_الطالب] = "منسحب", 1, 0)) / COUNT([p].[معرف_الطالب]) * 100, 2) AS نسبة_الانسحاب
                FROM المدرسين t
                LEFT JOIN الطلاب p ON t.معرف_المدرس = p.معرف_المدرس
                GROUP BY t.معرف_المدرس, t.اسم_المدرس, t.التخصص
                ORDER BY نسبة_التحصيل DESC, عدد_الطلاب_النشطين DESC
            </SQL>
        </Query>
    </Queries>
</AdditionalReportsQueries>