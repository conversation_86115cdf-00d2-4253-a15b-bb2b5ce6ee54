<?xml version="1.0" encoding="UTF-8"?>
<!-- نماذج محسنة لنظام محاسبة المعاهد الأهلية العراقية -->
<!-- Enhanced Forms for Iraqi Private Institutes Accounting System -->

<AccessFormsDesign>
  
  <!-- =============================================== -->
  <!-- نموذج الواجهة الرئيسية المحسن -->
  <!-- =============================================== -->
  <Form name="MainDashboard" type="Single">
    <Properties>
      <Caption>لوحة التحكم الرئيسية - نظام محاسبة المعاهد</Caption>
      <BackColor>#F5F5F5</BackColor>
      <BorderStyle>Dialog</BorderStyle>
      <ControlBox>True</ControlBox>
      <MinMaxButtons>Both</MinMaxButtons>
      <Width>1400</Width>
      <Height>900</Height>
      <AutoCenter>True</AutoCenter>
      <RightToLeft>True</RightToLeft>
      <RecordSelectors>False</RecordSelectors>
      <NavigationButtons>False</NavigationButtons>
      <ScrollBars>Neither</ScrollBars>
    </Properties>
    
    <Controls>
      <!-- Header Section -->
      <Rectangle name="rect_Header">
        <Properties>
          <Left>20</Left>
          <Top>20</Top>
          <Width>1360</Width>
          <Height>120</Height>
          <BackColor>#2C3E50</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Flat</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_SystemTitle">
        <Properties>
          <Left>50</Left>
          <Top>40</Top>
          <Width>600</Width>
          <Height>40</Height>
          <Caption>نظام محاسبة المعاهد الأهلية العراقية</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>20</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>
      
      <Label name="lbl_SystemSubtitle">
        <Properties>
          <Left>50</Left>
          <Top>85</Top>
          <Width>500</Width>
          <Height>25</Height>
          <Caption>Iraqi Private Institutes Accounting System</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <ForeColor>#BDC3C7</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>
      
      <!-- Statistics Cards Section -->
      <Rectangle name="rect_StatsBackground">
        <Properties>
          <Left>20</Left>
          <Top>160</Top>
          <Width>1360</Width>
          <Height>150</Height>
          <BackColor>#FFFFFF</BackColor>
          <BorderColor>#E0E0E0</BorderColor>
          <BorderWidth>1</BorderWidth>
          <SpecialEffect>Shadowed</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <!-- Card 1: Students Count -->
      <Rectangle name="rect_StudentsCard">
        <Properties>
          <Left>50</Left>
          <Top>180</Top>
          <Width>250</Width>
          <Height>110</Height>
          <BackColor>#3498DB</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_StudentsTitle">
        <Properties>
          <Left>70</Left>
          <Top>190</Top>
          <Width>210</Width>
          <Height>25</Height>
          <Caption>عدد الطلاب النشطين</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Center</TextAlign>
        </Properties>
      </Label>
      
      <TextBox name="txt_StudentsCount">
        <Properties>
          <Left>70</Left>
          <Top>220</Top>
          <Width>210</Width>
          <Height>50</Height>
          <ControlSource>=DCount("StudentID","Students","StudentStatus='Active'")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>24</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackColor>#3498DB</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <TextAlign>Center</TextAlign>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
        </Properties>
      </TextBox>
      
      <!-- Card 2: Teachers Count -->
      <Rectangle name="rect_TeachersCard">
        <Properties>
          <Left>320</Left>
          <Top>180</Top>
          <Width>250</Width>
          <Height>110</Height>
          <BackColor>#2ECC71</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_TeachersTitle">
        <Properties>
          <Left>340</Left>
          <Top>190</Top>
          <Width>210</Width>
          <Height>25</Height>
          <Caption>عدد المدرسين النشطين</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Center</TextAlign>
        </Properties>
      </Label>
      
      <TextBox name="txt_TeachersCount">
        <Properties>
          <Left>340</Left>
          <Top>220</Top>
          <Width>210</Width>
          <Height>50</Height>
          <ControlSource>=DCount("TeacherID","Teachers","IsActive=True")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>24</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackColor>#2ECC71</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <TextAlign>Center</TextAlign>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
        </Properties>
      </TextBox>
      
      <!-- Card 3: Total Revenue -->
      <Rectangle name="rect_RevenueCard">
        <Properties>
          <Left>590</Left>
          <Top>180</Top>
          <Width>250</Width>
          <Height>110</Height>
          <BackColor>#E67E22</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_RevenueTitle">
        <Properties>
          <Left>610</Left>
          <Top>190</Top>
          <Width>210</Width>
          <Height>25</Height>
          <Caption>إجمالي الإيرادات</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Center</TextAlign>
        </Properties>
      </Label>
      
      <TextBox name="txt_TotalRevenue">
        <Properties>
          <Left>610</Left>
          <Top>220</Top>
          <Width>210</Width>
          <Height>50</Height>
          <ControlSource>=DSum("AmountPaid","Payments")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>18</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackColor>#E67E22</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <TextAlign>Center</TextAlign>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
        </Properties>
      </TextBox>
      
      <!-- Card 4: Remaining Fees -->
      <Rectangle name="rect_RemainingCard">
        <Properties>
          <Left>860</Left>
          <Top>180</Top>
          <Width>250</Width>
          <Height>110</Height>
          <BackColor>#E74C3C</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_RemainingTitle">
        <Properties>
          <Left>880</Left>
          <Top>190</Top>
          <Width>210</Width>
          <Height>25</Height>
          <Caption>الأقساط المتبقية</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Center</TextAlign>
        </Properties>
      </Label>
      
      <TextBox name="txt_RemainingFees">
        <Properties>
          <Left>880</Left>
          <Top>220</Top>
          <Width>210</Width>
          <Height>50</Height>
          <ControlSource>=DSum("RemainingAmount","Students","StudentStatus='Active'")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>18</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackColor>#E74C3C</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <TextAlign>Center</TextAlign>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
        </Properties>
      </TextBox>
      
      <!-- Card 5: Total Expenses -->
      <Rectangle name="rect_ExpensesCard">
        <Properties>
          <Left>1130</Left>
          <Top>180</Top>
          <Width>250</Width>
          <Height>110</Height>
          <BackColor>#9B59B6</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_ExpensesTitle">
        <Properties>
          <Left>1150</Left>
          <Top>190</Top>
          <Width>210</Width>
          <Height>25</Height>
          <Caption>إجمالي المصاريف</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Center</TextAlign>
        </Properties>
      </Label>
      
      <TextBox name="txt_TotalExpenses">
        <Properties>
          <Left>1150</Left>
          <Top>220</Top>
          <Width>210</Width>
          <Height>50</Height>
          <ControlSource>=DSum("Amount","Expenses")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>18</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackColor>#9B59B6</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <TextAlign>Center</TextAlign>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
        </Properties>
      </TextBox>
      
      <!-- Navigation Buttons Section -->
      <Label name="lbl_NavigationTitle">
        <Properties>
          <Left>50</Left>
          <Top>340</Top>
          <Width>300</Width>
          <Height>30</Height>
          <Caption>الأقسام الرئيسية</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>16</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#2C3E50</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>
      
      <!-- Row 1 Buttons -->
      <CommandButton name="btn_Students">
        <Properties>
          <Left>50</Left>
          <Top>380</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>إدارة الطلاب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#3498DB</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#2980B9</HoverColor>
          <PressedColor>#21618C</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "StudentsForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Teachers">
        <Properties>
          <Left>270</Left>
          <Top>380</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>إدارة المدرسين</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#2ECC71</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#27AE60</HoverColor>
          <PressedColor>#1E8449</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "TeachersForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Payments">
        <Properties>
          <Left>490</Left>
          <Top>380</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>إدارة الدفعات</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#E67E22</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#D68910</HoverColor>
          <PressedColor>#B7950B</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "PaymentsForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Expenses">
        <Properties>
          <Left>710</Left>
          <Top>380</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>إدارة المصاريف</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#E74C3C</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#C0392B</HoverColor>
          <PressedColor>#A93226</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "ExpensesForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Courses">
        <Properties>
          <Left>930</Left>
          <Top>380</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>إدارة الدورات</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#9B59B6</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#8E44AD</HoverColor>
          <PressedColor>#7D3C98</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "CoursesForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Reports">
        <Properties>
          <Left>1150</Left>
          <Top>380</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>التقارير</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#34495E</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#2C3E50</HoverColor>
          <PressedColor>#1B2631</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "ReportsMenu"</OnClick>
        </Events>
      </CommandButton>
      
      <!-- Row 2 Buttons -->
      <CommandButton name="btn_Settings">
        <Properties>
          <Left>50</Left>
          <Top>480</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>الإعدادات</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#95A5A6</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#7F8C8D</HoverColor>
          <PressedColor>#566573</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "SettingsForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Backup">
        <Properties>
          <Left>270</Left>
          <Top>480</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>النسخ الاحتياطي</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#16A085</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#138D75</HoverColor>
          <PressedColor>#117A65</PressedColor>
        </Properties>
        <Events>
          <OnClick>Call BackupDatabase</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Help">
        <Properties>
          <Left>490</Left>
          <Top>480</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>المساعدة</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#F39C12</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#E67E22</HoverColor>
          <PressedColor>#D68910</PressedColor>
        </Properties>
        <Events>
          <OnClick>DoCmd.OpenForm "HelpForm"</OnClick>
        </Events>
      </CommandButton>
      
      <CommandButton name="btn_Exit">
        <Properties>
          <Left>1150</Left>
          <Top>480</Top>
          <Width>200</Width>
          <Height>80</Height>
          <Caption>خروج</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>14</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#C0392B</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
          <SpecialEffect>Raised</SpecialEffect>
          <HoverColor>#A93226</HoverColor>
          <PressedColor>#922B21</PressedColor>
        </Properties>
        <Events>
          <OnClick>
            If MsgBox("هل تريد إغلاق النظام؟", vbYesNo + vbQuestion, "تأكيد الخروج") = vbYes Then
              DoCmd.Quit
            End If
          </OnClick>
        </Events>
      </CommandButton>
      
      <!-- Footer Section -->
      <Rectangle name="rect_Footer">
        <Properties>
          <Left>20</Left>
          <Top>780</Top>
          <Width>1360</Width>
          <Height>80</Height>
          <BackColor>#ECF0F1</BackColor>
          <BorderColor>#BDC3C7</BorderColor>
          <BorderWidth>1</BorderWidth>
          <SpecialEffect>Sunken</SpecialEffect>
        </Properties>
      </Rectangle>
      
      <Label name="lbl_Copyright">
        <Properties>
          <Left>50</Left>
          <Top>800</Top>
          <Width>600</Width>
          <Height>20</Height>
          <Caption>نظام محاسبة المعاهد الأهلية العراقية - الإصدار 1.0</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <ForeColor>#7F8C8D</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>
      
      <TextBox name="txt_CurrentDateTime">
        <Properties>
          <Left>1000</Left>
          <Top>800</Top>
          <Width>350</Width>
          <Height>20</Height>
          <ControlSource>=Now()</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <ForeColor>#7F8C8D</ForeColor>
          <BackColor>#ECF0F1</BackColor>
          <BorderStyle>Transparent</BorderStyle>
          <TextAlign>Left</TextAlign>
          <Format>dddd, mmmm dd, yyyy - hh:nn AM/PM</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
        </Properties>
      </TextBox>
    </Controls>
    
    <Events>
      <OnLoad>
        Me.Refresh
        Me.Requery
      </OnLoad>
      <OnTimer>
        Me.txt_CurrentDateTime.Requery
        Me.txt_StudentsCount.Requery
        Me.txt_TeachersCount.Requery
        Me.txt_TotalRevenue.Requery
        Me.txt_RemainingFees.Requery
        Me.txt_TotalExpenses.Requery
      </OnTimer>
    </Events>
    
    <TimerInterval>30000</TimerInterval>
  </Form>

  <!-- =============================================== -->
  <!-- نموذج إدارة الطلاب -->
  <!-- =============================================== -->
  <Form name="StudentsForm" type="Continuous">
    <Properties>
      <Caption>إدارة الطلاب - نظام محاسبة المعاهد</Caption>
      <RecordSource>Students</RecordSource>
      <BackColor>#F8F9FA</BackColor>
      <BorderStyle>Sizable</BorderStyle>
      <ControlBox>True</ControlBox>
      <MinMaxButtons>Both</MinMaxButtons>
      <Width>1200</Width>
      <Height>700</Height>
      <AutoCenter>True</AutoCenter>
      <RightToLeft>True</RightToLeft>
      <AllowAdditions>True</AllowAdditions>
      <AllowDeletions>True</AllowDeletions>
      <AllowEdits>True</AllowEdits>
      <DataEntry>False</DataEntry>
    </Properties>

    <Controls>
      <!-- Header -->
      <Rectangle name="rect_StudentsHeader">
        <Properties>
          <Left>0</Left>
          <Top>0</Top>
          <Width>1200</Width>
          <Height>60</Height>
          <BackColor>#2C3E50</BackColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
      </Rectangle>

      <Label name="lbl_StudentsTitle">
        <Properties>
          <Left>20</Left>
          <Top>15</Top>
          <Width>300</Width>
          <Height>30</Height>
          <Caption>إدارة الطلاب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>16</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <!-- Search Section -->
      <Label name="lbl_Search">
        <Properties>
          <Left>20</Left>
          <Top>80</Top>
          <Width>80</Width>
          <Height>25</Height>
          <Caption>البحث:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <TextBox name="txt_Search">
        <Properties>
          <Left>110</Left>
          <Top>78</Top>
          <Width>200</Width>
          <Height>25</Height>
          <FontName>Tahoma</FontName>
          <FontSize>11</FontSize>
          <BorderColor>#BDC3C7</BorderColor>
        </Properties>
        <Events>
          <OnChange>
            Dim strFilter As String
            If Len(Me.txt_Search) > 0 Then
              strFilter = "StudentName Like '*" & Me.txt_Search & "*'"
              Me.Filter = strFilter
              Me.FilterOn = True
            Else
              Me.FilterOn = False
            End If
          </OnChange>
        </Events>
      </TextBox>

      <!-- Action Buttons -->
      <CommandButton name="btn_AddStudent">
        <Properties>
          <Left>350</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>إضافة طالب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#27AE60</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>DoCmd.GoToRecord , , acNewRec</OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_DeleteStudent">
        <Properties>
          <Left>460</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>حذف طالب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#E74C3C</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            If MsgBox("هل تريد حذف هذا الطالب؟", vbYesNo + vbQuestion) = vbYes Then
              DoCmd.RunCommand acCmdDeleteRecord
            End If
          </OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_PaymentHistory">
        <Properties>
          <Left>570</Left>
          <Top>75</Top>
          <Width>120</Width>
          <Height>30</Height>
          <Caption>تاريخ الدفعات</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#3498DB</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            DoCmd.OpenForm "PaymentHistoryForm", , , "StudentID=" & Me.StudentID
          </OnClick>
        </Events>
      </CommandButton>

      <!-- Data Fields -->
      <Label name="lbl_StudentID">
        <Properties>
          <Left>20</Left>
          <Top>120</Top>
          <Width>80</Width>
          <Height>20</Height>
          <Caption>رقم الطالب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_StudentID">
        <Properties>
          <Left>20</Left>
          <Top>140</Top>
          <Width>80</Width>
          <Height>25</Height>
          <ControlSource>StudentID</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <Label name="lbl_StudentName">
        <Properties>
          <Left>110</Left>
          <Top>120</Top>
          <Width>200</Width>
          <Height>20</Height>
          <Caption>اسم الطالب *</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#E74C3C</ForeColor>
        </Properties>
      </Label>

      <TextBox name="txt_StudentName">
        <Properties>
          <Left>110</Left>
          <Top>140</Top>
          <Width>200</Width>
          <Height>25</Height>
          <ControlSource>StudentName</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BorderColor>#BDC3C7</BorderColor>
        </Properties>
      </TextBox>

      <Label name="lbl_Gender">
        <Properties>
          <Left>320</Left>
          <Top>120</Top>
          <Width>80</Width>
          <Height>20</Height>
          <Caption>الجنس</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <ComboBox name="cmb_Gender">
        <Properties>
          <Left>320</Left>
          <Top>140</Top>
          <Width>80</Width>
          <Height>25</Height>
          <ControlSource>Gender</ControlSource>
          <RowSourceType>Value List</RowSourceType>
          <RowSource>ذكر;أنثى</RowSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
        </Properties>
      </ComboBox>

      <Label name="lbl_Age">
        <Properties>
          <Left>410</Left>
          <Top>120</Top>
          <Width>60</Width>
          <Height>20</Height>
          <Caption>العمر</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_Age">
        <Properties>
          <Left>410</Left>
          <Top>140</Top>
          <Width>60</Width>
          <Height>25</Height>
          <ControlSource>Age</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <InputMask>99</InputMask>
        </Properties>
      </TextBox>

      <Label name="lbl_Grade">
        <Properties>
          <Left>480</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>الصف</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_Grade">
        <Properties>
          <Left>480</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>Grade</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
        </Properties>
      </TextBox>

      <Label name="lbl_Course">
        <Properties>
          <Left>590</Left>
          <Top>120</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>الدورة</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <ComboBox name="cmb_Course">
        <Properties>
          <Left>590</Left>
          <Top>140</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>CourseID</ControlSource>
          <RowSourceType>Table/Query</RowSourceType>
          <RowSource>SELECT CourseID, CourseName FROM Courses WHERE IsActive=True ORDER BY CourseName</RowSource>
          <ColumnCount>2</ColumnCount>
          <ColumnWidths>0;150</ColumnWidths>
          <BoundColumn>1</BoundColumn>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
        </Properties>
      </ComboBox>

      <Label name="lbl_Teacher">
        <Properties>
          <Left>750</Left>
          <Top>120</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>المدرس</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <ComboBox name="cmb_Teacher">
        <Properties>
          <Left>750</Left>
          <Top>140</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>TeacherID</ControlSource>
          <RowSourceType>Table/Query</RowSourceType>
          <RowSource>SELECT TeacherID, TeacherName FROM Teachers WHERE IsActive=True ORDER BY TeacherName</RowSource>
          <ColumnCount>2</ColumnCount>
          <ColumnWidths>0;150</ColumnWidths>
          <BoundColumn>1</BoundColumn>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
        </Properties>
      </ComboBox>

      <!-- Financial Information Row -->
      <Label name="lbl_TotalFee">
        <Properties>
          <Left>20</Left>
          <Top>180</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>القسط الكلي</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_TotalFee">
        <Properties>
          <Left>20</Left>
          <Top>200</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>TotalFee</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
        </Properties>
        <Events>
          <OnChange>
            Me.txt_RemainingAmount = Nz(Me.txt_TotalFee, 0) - Nz(Me.txt_PaidAmount, 0)
          </OnChange>
        </Events>
      </TextBox>

      <Label name="lbl_PaidAmount">
        <Properties>
          <Left>130</Left>
          <Top>180</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>المبلغ المدفوع</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_PaidAmount">
        <Properties>
          <Left>130</Left>
          <Top>200</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>PaidAmount</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <Label name="lbl_RemainingAmount">
        <Properties>
          <Left>240</Left>
          <Top>180</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>المبلغ المتبقي</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_RemainingAmount">
        <Properties>
          <Left>240</Left>
          <Top>200</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>RemainingAmount</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <Label name="lbl_RegistrationDate">
        <Properties>
          <Left>350</Left>
          <Top>180</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>تاريخ التسجيل</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_RegistrationDate">
        <Properties>
          <Left>350</Left>
          <Top>200</Top>
          <Width>120</Width>
          <Height>25</Height>
          <ControlSource>RegistrationDate</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Short Date</Format>
          <DefaultValue>=Date()</DefaultValue>
        </Properties>
      </TextBox>

      <Label name="lbl_StudentStatus">
        <Properties>
          <Left>480</Left>
          <Top>180</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>حالة الطالب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <ComboBox name="cmb_StudentStatus">
        <Properties>
          <Left>480</Left>
          <Top>200</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>StudentStatus</ControlSource>
          <RowSourceType>Value List</RowSourceType>
          <RowSource>نشط;منسحب;مجمد</RowSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
          <DefaultValue>"نشط"</DefaultValue>
        </Properties>
      </ComboBox>

      <!-- Contact Information -->
      <Label name="lbl_ParentName">
        <Properties>
          <Left>590</Left>
          <Top>180</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>اسم ولي الأمر</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_ParentName">
        <Properties>
          <Left>590</Left>
          <Top>200</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>ParentName</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
        </Properties>
      </TextBox>

      <Label name="lbl_ParentPhone">
        <Properties>
          <Left>750</Left>
          <Top>180</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>هاتف ولي الأمر</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_ParentPhone">
        <Properties>
          <Left>750</Left>
          <Top>200</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>ParentPhone</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <InputMask>00000000000</InputMask>
        </Properties>
      </TextBox>

      <!-- Quick Payment Button -->
      <CommandButton name="btn_QuickPayment">
        <Properties>
          <Left>920</Left>
          <Top>140</Top>
          <Width>120</Width>
          <Height>40</Height>
          <Caption>دفعة سريعة</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#27AE60</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            DoCmd.OpenForm "QuickPaymentForm", , , "StudentID=" & Me.StudentID, , acDialog
            Me.Requery
          </OnClick>
        </Events>
      </CommandButton>

      <!-- Student Withdrawal Button -->
      <CommandButton name="btn_Withdrawal">
        <Properties>
          <Left>920</Left>
          <Top>190</Top>
          <Width>120</Width>
          <Height>40</Height>
          <Caption>انسحاب الطالب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#E74C3C</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            DoCmd.OpenForm "WithdrawalForm", , , "StudentID=" & Me.StudentID, , acDialog
            Me.Requery
          </OnClick>
        </Events>
      </CommandButton>
    </Controls>

    <Events>
      <OnLoad>
        Me.OrderBy = "StudentName"
        Me.OrderByOn = True
      </OnLoad>
      <OnCurrent>
        ' Update calculated fields
        If Not IsNull(Me.TotalFee) And Not IsNull(Me.PaidAmount) Then
          Me.RemainingAmount = Me.TotalFee - Me.PaidAmount
        End If
      </OnCurrent>
    </Events>
  </Form>

  <!-- =============================================== -->
  <!-- نموذج إدارة المدرسين -->
  <!-- =============================================== -->
  <Form name="TeachersForm" type="Continuous">
    <Properties>
      <Caption>إدارة المدرسين - نظام محاسبة المعاهد</Caption>
      <RecordSource>Teachers</RecordSource>
      <BackColor>#F8F9FA</BackColor>
      <BorderStyle>Sizable</BorderStyle>
      <ControlBox>True</ControlBox>
      <MinMaxButtons>Both</MinMaxButtons>
      <Width>1000</Width>
      <Height>600</Height>
      <AutoCenter>True</AutoCenter>
      <RightToLeft>True</RightToLeft>
      <AllowAdditions>True</AllowAdditions>
      <AllowDeletions>True</AllowDeletions>
      <AllowEdits>True</AllowEdits>
    </Properties>

    <Controls>
      <!-- Header -->
      <Rectangle name="rect_TeachersHeader">
        <Properties>
          <Left>0</Left>
          <Top>0</Top>
          <Width>1000</Width>
          <Height>60</Height>
          <BackColor>#2C3E50</BackColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
      </Rectangle>

      <Label name="lbl_TeachersTitle">
        <Properties>
          <Left>20</Left>
          <Top>15</Top>
          <Width>300</Width>
          <Height>30</Height>
          <Caption>إدارة المدرسين</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>16</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <!-- Search and Action Buttons -->
      <Label name="lbl_TeacherSearch">
        <Properties>
          <Left>20</Left>
          <Top>80</Top>
          <Width>80</Width>
          <Height>25</Height>
          <Caption>البحث:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <TextBox name="txt_TeacherSearch">
        <Properties>
          <Left>110</Left>
          <Top>78</Top>
          <Width>200</Width>
          <Height>25</Height>
          <FontName>Tahoma</FontName>
          <FontSize>11</FontSize>
          <BorderColor>#BDC3C7</BorderColor>
        </Properties>
        <Events>
          <OnChange>
            Dim strFilter As String
            If Len(Me.txt_TeacherSearch) > 0 Then
              strFilter = "TeacherName Like '*" & Me.txt_TeacherSearch & "*'"
              Me.Filter = strFilter
              Me.FilterOn = True
            Else
              Me.FilterOn = False
            End If
          </OnChange>
        </Events>
      </TextBox>

      <CommandButton name="btn_AddTeacher">
        <Properties>
          <Left>350</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>إضافة مدرس</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#27AE60</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>DoCmd.GoToRecord , , acNewRec</OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_DeleteTeacher">
        <Properties>
          <Left>460</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>حذف مدرس</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#E74C3C</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            If MsgBox("هل تريد حذف هذا المدرس؟", vbYesNo + vbQuestion) = vbYes Then
              DoCmd.RunCommand acCmdDeleteRecord
            End If
          </OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_TeacherEarnings">
        <Properties>
          <Left>570</Left>
          <Top>75</Top>
          <Width>120</Width>
          <Height>30</Height>
          <Caption>أرباح المدرس</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#3498DB</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            DoCmd.OpenForm "TeacherEarningsForm", , , "TeacherID=" & Me.TeacherID
          </OnClick>
        </Events>
      </CommandButton>

      <!-- Data Fields -->
      <Label name="lbl_TeacherID">
        <Properties>
          <Left>20</Left>
          <Top>120</Top>
          <Width>80</Width>
          <Height>20</Height>
          <Caption>رقم المدرس</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_TeacherID">
        <Properties>
          <Left>20</Left>
          <Top>140</Top>
          <Width>80</Width>
          <Height>25</Height>
          <ControlSource>TeacherID</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <Label name="lbl_TeacherName">
        <Properties>
          <Left>110</Left>
          <Top>120</Top>
          <Width>200</Width>
          <Height>20</Height>
          <Caption>اسم المدرس *</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#E74C3C</ForeColor>
        </Properties>
      </Label>

      <TextBox name="txt_TeacherName">
        <Properties>
          <Left>110</Left>
          <Top>140</Top>
          <Width>200</Width>
          <Height>25</Height>
          <ControlSource>TeacherName</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BorderColor>#BDC3C7</BorderColor>
        </Properties>
      </TextBox>

      <Label name="lbl_Specialization">
        <Properties>
          <Left>320</Left>
          <Top>120</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>التخصص</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_Specialization">
        <Properties>
          <Left>320</Left>
          <Top>140</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>Specialization</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
        </Properties>
      </TextBox>

      <Label name="lbl_TeacherPhone">
        <Properties>
          <Left>480</Left>
          <Top>120</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>رقم الهاتف</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_TeacherPhone">
        <Properties>
          <Left>480</Left>
          <Top>140</Top>
          <Width>120</Width>
          <Height>25</Height>
          <ControlSource>PhoneNumber</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <InputMask>00000000000</InputMask>
        </Properties>
      </TextBox>

      <Label name="lbl_SalaryType">
        <Properties>
          <Left>610</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>نوع الأجر</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <ComboBox name="cmb_SalaryType">
        <Properties>
          <Left>610</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>SalaryType</ControlSource>
          <RowSourceType>Value List</RowSourceType>
          <RowSource>نسبة;ثابت</RowSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
          <DefaultValue>"نسبة"</DefaultValue>
        </Properties>
        <Events>
          <OnChange>
            If Me.cmb_SalaryType = "ثابت" Then
              Me.txt_MonthlySalary.Enabled = True
              Me.txt_MonthlySalary.Locked = False
              Me.txt_SharePercentage.Enabled = False
              Me.txt_SharePercentage.Locked = True
              Me.txt_SharePercentage.Value = 0
            Else
              Me.txt_MonthlySalary.Enabled = False
              Me.txt_MonthlySalary.Locked = True
              Me.txt_MonthlySalary.Value = 0
              Me.txt_SharePercentage.Enabled = True
              Me.txt_SharePercentage.Locked = False
            End If
          </OnChange>
        </Events>
      </ComboBox>

      <Label name="lbl_IsActive">
        <Properties>
          <Left>720</Left>
          <Top>120</Top>
          <Width>80</Width>
          <Height>20</Height>
          <Caption>نشط</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <CheckBox name="chk_IsActive">
        <Properties>
          <Left>720</Left>
          <Top>140</Top>
          <Width>80</Width>
          <Height>25</Height>
          <ControlSource>IsActive</ControlSource>
          <DefaultValue>True</DefaultValue>
        </Properties>
      </CheckBox>

      <!-- Second Row - Financial Information -->
      <Label name="lbl_MonthlySalary">
        <Properties>
          <Left>20</Left>
          <Top>180</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>الأجر الشهري</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_MonthlySalary">
        <Properties>
          <Left>20</Left>
          <Top>200</Top>
          <Width>120</Width>
          <Height>25</Height>
          <ControlSource>MonthlySalary</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <Label name="lbl_SharePercentage">
        <Properties>
          <Left>150</Left>
          <Top>180</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>نسبة المشاركة %</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_SharePercentage">
        <Properties>
          <Left>150</Left>
          <Top>200</Top>
          <Width>120</Width>
          <Height>25</Height>
          <ControlSource>SharePercentage</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Percent</Format>
          <DefaultValue>30</DefaultValue>
        </Properties>
      </TextBox>

      <!-- Student Count Display -->
      <Label name="lbl_StudentCount">
        <Properties>
          <Left>280</Left>
          <Top>180</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>عدد الطلاب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_StudentCount">
        <Properties>
          <Left>280</Left>
          <Top>200</Top>
          <Width>120</Width>
          <Height>25</Height>
          <ControlSource>=DCount("StudentID","Students","TeacherID=" & [TeacherID] & " AND StudentStatus='Active'")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <!-- Monthly Earnings Display -->
      <Label name="lbl_MonthlyEarnings">
        <Properties>
          <Left>410</Left>
          <Top>180</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>الأرباح الشهرية</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_MonthlyEarnings">
        <Properties>
          <Left>410</Left>
          <Top>200</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>=DSum("TeacherShare","TeacherEarnings","TeacherID=" & [TeacherID] & " AND Month=" & Month(Date()) & " AND Year=" & Year(Date()))</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <!-- Action Buttons for Individual Teacher -->
      <CommandButton name="btn_ViewStudents">
        <Properties>
          <Left>580</Left>
          <Top>180</Top>
          <Width>100</Width>
          <Height>45</Height>
          <Caption>عرض الطلاب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#3498DB</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            DoCmd.OpenForm "StudentsForm", , , "TeacherID=" & Me.TeacherID
          </OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_CalculateEarnings">
        <Properties>
          <Left>690</Left>
          <Top>180</Top>
          <Width>100</Width>
          <Height>45</Height>
          <Caption>حساب الأرباح</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#27AE60</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            Call CalculateTeacherEarnings(Me.TeacherID)
            Me.txt_MonthlyEarnings.Requery
            MsgBox "تم حساب أرباح المدرس بنجاح", vbInformation
          </OnClick>
        </Events>
      </CommandButton>
    </Controls>

    <Events>
      <OnLoad>
        Me.OrderBy = "TeacherName"
        Me.OrderByOn = True
      </OnLoad>
      <OnCurrent>
        ' Update salary type dependent fields
        If Me.cmb_SalaryType = "ثابت" Then
          Me.txt_MonthlySalary.Enabled = True
          Me.txt_MonthlySalary.Locked = False
          Me.txt_SharePercentage.Enabled = False
          Me.txt_SharePercentage.Locked = True
        Else
          Me.txt_MonthlySalary.Enabled = False
          Me.txt_MonthlySalary.Locked = True
          Me.txt_SharePercentage.Enabled = True
          Me.txt_SharePercentage.Locked = False
        End If

        ' Requery calculated fields
        Me.txt_StudentCount.Requery
        Me.txt_MonthlyEarnings.Requery
      </OnCurrent>
    </Events>
  </Form>

  <!-- =============================================== -->
  <!-- نموذج إدارة الدفعات -->
  <!-- =============================================== -->
  <Form name="PaymentsForm" type="Continuous">
    <Properties>
      <Caption>إدارة الدفعات - نظام محاسبة المعاهد</Caption>
      <RecordSource>SELECT P.*, S.StudentName FROM Payments P INNER JOIN Students S ON P.StudentID = S.StudentID ORDER BY P.PaymentDate DESC</RecordSource>
      <BackColor>#F8F9FA</BackColor>
      <BorderStyle>Sizable</BorderStyle>
      <ControlBox>True</ControlBox>
      <MinMaxButtons>Both</MinMaxButtons>
      <Width>1100</Width>
      <Height>650</Height>
      <AutoCenter>True</AutoCenter>
      <RightToLeft>True</RightToLeft>
      <AllowAdditions>True</AllowAdditions>
      <AllowDeletions>True</AllowDeletions>
      <AllowEdits>True</AllowEdits>
    </Properties>

    <Controls>
      <!-- Header -->
      <Rectangle name="rect_PaymentsHeader">
        <Properties>
          <Left>0</Left>
          <Top>0</Top>
          <Width>1100</Width>
          <Height>60</Height>
          <BackColor>#2C3E50</BackColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
      </Rectangle>

      <Label name="lbl_PaymentsTitle">
        <Properties>
          <Left>20</Left>
          <Top>15</Top>
          <Width>300</Width>
          <Height>30</Height>
          <Caption>إدارة الدفعات</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>16</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#FFFFFF</ForeColor>
          <BackStyle>Transparent</BackStyle>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <!-- Filter and Search Section -->
      <Label name="lbl_PaymentSearch">
        <Properties>
          <Left>20</Left>
          <Top>80</Top>
          <Width>80</Width>
          <Height>25</Height>
          <Caption>البحث:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>12</FontSize>
          <FontWeight>Bold</FontWeight>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <TextBox name="txt_PaymentSearch">
        <Properties>
          <Left>110</Left>
          <Top>78</Top>
          <Width>150</Width>
          <Height>25</Height>
          <FontName>Tahoma</FontName>
          <FontSize>11</FontSize>
          <BorderColor>#BDC3C7</BorderColor>
        </Properties>
        <Events>
          <OnChange>
            Dim strFilter As String
            If Len(Me.txt_PaymentSearch) > 0 Then
              strFilter = "StudentName Like '*" & Me.txt_PaymentSearch & "*'"
              Me.Filter = strFilter
              Me.FilterOn = True
            Else
              Me.FilterOn = False
            End If
          </OnChange>
        </Events>
      </TextBox>

      <Label name="lbl_DateFilter">
        <Properties>
          <Left>280</Left>
          <Top>80</Top>
          <Width>60</Width>
          <Height>25</Height>
          <Caption>من تاريخ:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <TextBox name="txt_DateFrom">
        <Properties>
          <Left>350</Left>
          <Top>78</Top>
          <Width>100</Width>
          <Height>25</Height>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Short Date</Format>
        </Properties>
        <Events>
          <OnChange>Call ApplyDateFilter</OnChange>
        </Events>
      </TextBox>

      <Label name="lbl_DateTo">
        <Properties>
          <Left>460</Left>
          <Top>80</Top>
          <Width>60</Width>
          <Height>25</Height>
          <Caption>إلى تاريخ:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <TextAlign>Right</TextAlign>
        </Properties>
      </Label>

      <TextBox name="txt_DateTo">
        <Properties>
          <Left>530</Left>
          <Top>78</Top>
          <Width>100</Width>
          <Height>25</Height>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Short Date</Format>
        </Properties>
        <Events>
          <OnChange>Call ApplyDateFilter</OnChange>
        </Events>
      </TextBox>

      <!-- Action Buttons -->
      <CommandButton name="btn_AddPayment">
        <Properties>
          <Left>650</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>إضافة دفعة</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#27AE60</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>DoCmd.GoToRecord , , acNewRec</OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_DeletePayment">
        <Properties>
          <Left>760</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>حذف دفعة</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#E74C3C</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            If MsgBox("هل تريد حذف هذه الدفعة؟", vbYesNo + vbQuestion) = vbYes Then
              DoCmd.RunCommand acCmdDeleteRecord
            End If
          </OnClick>
        </Events>
      </CommandButton>

      <CommandButton name="btn_PrintReceipt">
        <Properties>
          <Left>870</Left>
          <Top>75</Top>
          <Width>100</Width>
          <Height>30</Height>
          <Caption>طباعة إيصال</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <BackColor>#3498DB</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            DoCmd.OpenReport "PaymentReceiptReport", acViewPreview, , "PaymentID=" & Me.PaymentID
          </OnClick>
        </Events>
      </CommandButton>

      <!-- Data Fields -->
      <Label name="lbl_PaymentID">
        <Properties>
          <Left>20</Left>
          <Top>120</Top>
          <Width>80</Width>
          <Height>20</Height>
          <Caption>رقم الدفعة</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_PaymentID">
        <Properties>
          <Left>20</Left>
          <Top>140</Top>
          <Width>80</Width>
          <Height>25</Height>
          <ControlSource>PaymentID</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <Label name="lbl_PaymentStudent">
        <Properties>
          <Left>110</Left>
          <Top>120</Top>
          <Width>150</Width>
          <Height>20</Height>
          <Caption>الطالب *</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#E74C3C</ForeColor>
        </Properties>
      </Label>

      <ComboBox name="cmb_PaymentStudent">
        <Properties>
          <Left>110</Left>
          <Top>140</Top>
          <Width>150</Width>
          <Height>25</Height>
          <ControlSource>StudentID</ControlSource>
          <RowSourceType>Table/Query</RowSourceType>
          <RowSource>SELECT StudentID, StudentName FROM Students WHERE StudentStatus='Active' ORDER BY StudentName</RowSource>
          <ColumnCount>2</ColumnCount>
          <ColumnWidths>0;150</ColumnWidths>
          <BoundColumn>1</BoundColumn>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
        </Properties>
        <Events>
          <OnChange>
            ' Update remaining amount display
            If Not IsNull(Me.cmb_PaymentStudent) Then
              Dim rs As Recordset
              Set rs = CurrentDb.OpenRecordset("SELECT RemainingAmount FROM Students WHERE StudentID=" & Me.cmb_PaymentStudent)
              If Not rs.EOF Then
                Me.txt_RemainingDisplay.Value = rs!RemainingAmount
              End If
              rs.Close
            End If
          </OnChange>
        </Events>
      </ComboBox>

      <Label name="lbl_AmountPaid">
        <Properties>
          <Left>270</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>المبلغ المدفوع *</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <ForeColor>#E74C3C</ForeColor>
        </Properties>
      </Label>

      <TextBox name="txt_AmountPaid">
        <Properties>
          <Left>270</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>AmountPaid</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
        </Properties>
      </TextBox>

      <Label name="lbl_PaymentDate">
        <Properties>
          <Left>380</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>تاريخ الدفع</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_PaymentDate">
        <Properties>
          <Left>380</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>PaymentDate</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Short Date</Format>
          <DefaultValue>=Date()</DefaultValue>
        </Properties>
      </TextBox>

      <Label name="lbl_PaymentMethod">
        <Properties>
          <Left>490</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>طريقة الدفع</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <ComboBox name="cmb_PaymentMethod">
        <Properties>
          <Left>490</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>PaymentMethod</ControlSource>
          <RowSourceType>Value List</RowSourceType>
          <RowSource>نقدي;تحويل بنكي;شيك;بطاقة ائتمان</RowSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <LimitToList>True</LimitToList>
          <DefaultValue>"نقدي"</DefaultValue>
        </Properties>
      </ComboBox>

      <Label name="lbl_ReceiptNumber">
        <Properties>
          <Left>600</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>رقم الإيصال</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_ReceiptNumber">
        <Properties>
          <Left>600</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <ControlSource>ReceiptNumber</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
        </Properties>
      </TextBox>

      <!-- Remaining Amount Display -->
      <Label name="lbl_RemainingDisplay">
        <Properties>
          <Left>710</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>20</Height>
          <Caption>المبلغ المتبقي</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_RemainingDisplay">
        <Properties>
          <Left>710</Left>
          <Top>140</Top>
          <Width>100</Width>
          <Height>25</Height>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
        </Properties>
      </TextBox>

      <!-- Quick Action Buttons -->
      <CommandButton name="btn_UpdateStudent">
        <Properties>
          <Left>820</Left>
          <Top>120</Top>
          <Width>100</Width>
          <Height>45</Height>
          <Caption>تحديث الطالب</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
          <BackColor>#27AE60</BackColor>
          <ForeColor>#FFFFFF</ForeColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
        <Events>
          <OnClick>
            ' Update student's paid and remaining amounts
            If Not IsNull(Me.StudentID) And Not IsNull(Me.AmountPaid) Then
              Dim db As Database
              Set db = CurrentDb
              db.Execute "UPDATE Students SET PaidAmount = PaidAmount + " & Me.AmountPaid & ", RemainingAmount = TotalFee - (PaidAmount + " & Me.AmountPaid & ") WHERE StudentID = " & Me.StudentID
              MsgBox "تم تحديث بيانات الطالب بنجاح", vbInformation
              Me.txt_RemainingDisplay.Requery
            End If
          </OnClick>
        </Events>
      </CommandButton>

      <!-- Summary Section -->
      <Rectangle name="rect_PaymentSummary">
        <Properties>
          <Left>20</Left>
          <Top>180</Top>
          <Width>900</Width>
          <Height>60</Height>
          <BackColor>#ECF0F1</BackColor>
          <BorderColor>#BDC3C7</BorderColor>
          <BorderWidth>1</BorderWidth>
          <SpecialEffect>Sunken</SpecialEffect>
        </Properties>
      </Rectangle>

      <Label name="lbl_TodayTotal">
        <Properties>
          <Left>40</Left>
          <Top>190</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>إجمالي اليوم:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_TodayTotal">
        <Properties>
          <Left>40</Left>
          <Top>210</Top>
          <Width>120</Width>
          <Height>20</Height>
          <ControlSource>=DSum("AmountPaid","Payments","PaymentDate=Date()")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
      </TextBox>

      <Label name="lbl_MonthTotal">
        <Properties>
          <Left>180</Left>
          <Top>190</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>إجمالي الشهر:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_MonthTotal">
        <Properties>
          <Left>180</Left>
          <Top>210</Top>
          <Width>120</Width>
          <Height>20</Height>
          <ControlSource>=DSum("AmountPaid","Payments","Month(PaymentDate)=Month(Date()) AND Year(PaymentDate)=Year(Date())")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
      </TextBox>

      <Label name="lbl_YearTotal">
        <Properties>
          <Left>320</Left>
          <Top>190</Top>
          <Width>120</Width>
          <Height>20</Height>
          <Caption>إجمالي السنة:</Caption>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <FontWeight>Bold</FontWeight>
        </Properties>
      </Label>

      <TextBox name="txt_YearTotal">
        <Properties>
          <Left>320</Left>
          <Top>210</Top>
          <Width>120</Width>
          <Height>20</Height>
          <ControlSource>=DSum("AmountPaid","Payments","Year(PaymentDate)=Year(Date())")</ControlSource>
          <FontName>Tahoma</FontName>
          <FontSize>10</FontSize>
          <Format>Currency</Format>
          <Enabled>False</Enabled>
          <Locked>True</Locked>
          <BackColor>#ECF0F1</BackColor>
          <BorderStyle>Transparent</BorderStyle>
        </Properties>
      </TextBox>
    </Controls>

    <Events>
      <OnLoad>
        Me.OrderBy = "PaymentDate DESC"
        Me.OrderByOn = True
      </OnLoad>
      <OnCurrent>
        ' Update remaining amount display when record changes
        If Not IsNull(Me.StudentID) Then
          Dim rs As Recordset
          Set rs = CurrentDb.OpenRecordset("SELECT RemainingAmount FROM Students WHERE StudentID=" & Me.StudentID)
          If Not rs.EOF Then
            Me.txt_RemainingDisplay.Value = rs!RemainingAmount
          End If
          rs.Close
        End If
      </OnCurrent>
      <OnAfterUpdate>
        ' Refresh summary totals after update
        Me.txt_TodayTotal.Requery
        Me.txt_MonthTotal.Requery
        Me.txt_YearTotal.Requery
      </OnAfterUpdate>
    </Events>
  </Form>
</AccessFormsDesign>
