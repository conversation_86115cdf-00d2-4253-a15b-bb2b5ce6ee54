# نظام محاسبة المعاهد الأهلية العراقية
## Iraqi Private Institutes Accounting System

<div align="center">

![System Logo](https://img.shields.io/badge/نظام_محاسبة_المعاهد-v1.0-blue?style=for-the-badge&logo=microsoft-access)

[![Microsoft Access](https://img.shields.io/badge/Microsoft_Access-2016+-red?style=flat-square&logo=microsoft-access)](https://www.microsoft.com/en-us/microsoft-365/access)
[![Windows](https://img.shields.io/badge/Windows-10+-blue?style=flat-square&logo=windows)](https://www.microsoft.com/windows)
[![Arabic](https://img.shields.io/badge/Language-Arabic-green?style=flat-square)](README.md)
[![License](https://img.shields.io/badge/License-MIT-yellow?style=flat-square)](LICENSE)

</div>

---

## 📋 نظرة عامة

نظام محاسبة المعاهد الأهلية العراقية هو نظام شامل ومتكامل مصمم خصيصاً لإدارة العمليات المحاسبية والإدارية في المعاهد الأهلية العراقية. يوفر النظام حلولاً متقدمة لإدارة الطلاب والمدرسين والمدفوعات والتقارير بتصميم عصري ومتوافق مع البيئة العراقية.

## ✨ المميزات الرئيسية

### 🎓 إدارة الطلاب
- تسجيل وإدارة بيانات الطلاب الشاملة
- متابعة الأقساط والمدفوعات
- نظام انسحاب الطلاب مع حساب المبالغ المستردة
- ربط الطلاب بالدورات والمدرسين

### 👨‍🏫 إدارة المدرسين
- تسجيل بيانات المدرسين وتخصصاتهم
- نظام أجور مرن (ثابت أو نسبة من الأرباح)
- حساب أرباح المدرسين تلقائياً
- تتبع عدد الطلاب لكل مدرس

### 💰 نظام الدفعات المتقدم
- تسجيل جميع أنواع المدفوعات
- إصدار إيصالات مطبوعة
- تتبع طرق الدفع المختلفة
- تحديث أرصدة الطلاب تلقائياً

### 📊 التقارير الشاملة
- تقارير الطلاب التفصيلية
- تقارير الدفعات الشهرية
- تقارير أرباح المدرسين
- تقارير المصاريف والأرباح الصافية

### 🎨 تصميم نيومورفي عصري
- واجهة مستخدم عصرية بتصميم Neumorphic
- ألوان هادئة وظلال ناعمة
- تصميم متجاوب ومريح للعين
- دعم كامل للغة العربية من اليمين إلى اليسار

### 🔒 الأمان والنسخ الاحتياطي
- نظام نسخ احتياطي تلقائي
- حماية البيانات
- تشفير المعلومات الحساسة

## 🛠️ متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10 أو أحدث
- **Microsoft Access**: 2016 أو أحدث
- **الذاكرة**: 4 جيجابايت RAM
- **مساحة القرص**: 500 ميجابايت
- **الشاشة**: دقة 1366x768

### المستحسن
- **نظام التشغيل**: Windows 11
- **Microsoft Access**: 2021 أو Microsoft 365
- **الذاكرة**: 8 جيجابايت RAM
- **مساحة القرص**: 2 جيجابايت
- **الشاشة**: دقة 1920x1080

## 📦 التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/iraqi-institutes-accounting.git
cd iraqi-institutes-accounting
```

### 2. إنشاء قاعدة البيانات
```powershell
# تشغيل سكريپت إنشاء قاعدة البيانات
powershell -ExecutionPolicy Bypass -File "CreateBasicDatabase.ps1"
```

### 3. فتح النظام
1. افتح ملف `InstituteAccounting.accdb`
2. اتبع معالج الإعداد الأولي
3. أدخل بيانات المعهد الأساسية

## 🚀 البدء السريع

### الخطوة 1: إعداد المعهد
1. افتح النظام
2. اذهب إلى "الإعدادات"
3. أدخل اسم المعهد وبياناته

### الخطوة 2: إضافة الدورات
1. اضغط على "إدارة الدورات"
2. أضف الدورات المتاحة في المعهد
3. حدد الرسوم لكل دورة

### الخطوة 3: تسجيل المدرسين
1. اذهب إلى "إدارة المدرسين"
2. أضف بيانات المدرسين
3. حدد نوع الأجر لكل مدرس

### الخطوة 4: تسجيل الطلاب
1. افتح "إدارة الطلاب"
2. أضف بيانات الطلاب
3. اربط كل طالب بدورة ومدرس

## 📁 هيكل المشروع

```
نظام-محاسبة-المعاهد/
├── InstituteAccounting.accdb          # قاعدة البيانات الرئيسية
├── CreateBasicDatabase.ps1            # سكريپت إنشاء قاعدة البيانات
├── Enhanced_Forms_Design.xml          # تصميم النماذج
├── Enhanced_Reports_Design.xml        # تصميم التقارير
├── Enhanced_VBA_Modules.vba          # وحدات VBA
├── دليل_المستخدم.md                  # دليل المستخدم الشامل
├── README_Enhanced.md                # هذا الملف
└── docs/                            # مجلد الوثائق
    ├── screenshots/                 # لقطات الشاشة
    └── user-manual/                # دليل المستخدم التفصيلي
```

## 📚 الوثائق

- [دليل المستخدم الشامل](دليل_المستخدم.md)
- [دليل التثبيت](docs/installation-guide.md)
- [دليل المطور](docs/developer-guide.md)
- [الأسئلة الشائعة](docs/faq.md)

## 🔧 التطوير والمساهمة

### إعداد بيئة التطوير
1. تأكد من تثبيت Microsoft Access
2. استنسخ المستودع
3. افتح ملف قاعدة البيانات في Access

### المساهمة في المشروع
1. Fork المشروع
2. أنشئ فرع جديد (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. افتح Pull Request

## 🐛 الإبلاغ عن الأخطاء

إذا واجهت أي مشاكل أو أخطاء، يرجى:
1. التحقق من [الأسئلة الشائعة](docs/faq.md)
2. البحث في [Issues الموجودة](https://github.com/your-repo/issues)
3. إنشاء [Issue جديد](https://github.com/your-repo/issues/new) مع تفاصيل المشكلة

## 📈 خارطة الطريق

### الإصدار 1.1 (قريباً)
- [ ] دعم قواعد بيانات SQL Server
- [ ] تطبيق ويب مصاحب
- [ ] تقارير تفاعلية متقدمة
- [ ] نظام إشعارات

### الإصدار 1.2 (مستقبلي)
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] ذكاء اصطناعي للتنبؤات المالية
- [ ] دعم متعدد المعاهد

## 🤝 الشركاء والداعمون

- [Microsoft](https://www.microsoft.com) - لتوفير منصة Access
- [المعاهد الأهلية العراقية](https://example.com) - للتعاون في التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 👥 الفريق

- **المطور الرئيسي**: [اسم المطور](https://github.com/developer)
- **مصمم الواجهات**: [اسم المصمم](https://github.com/designer)
- **محلل الأنظمة**: [اسم المحلل](https://github.com/analyst)

## 📞 التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع الإلكتروني**: [www.institute-accounting.com](https://www.institute-accounting.com)
- **تليجرام**: [@institute_accounting](https://t.me/institute_accounting)

## 🙏 شكر وتقدير

نشكر جميع المساهمين والمختبرين الذين ساعدوا في تطوير هذا النظام:
- المعاهد الأهلية العراقية للتعاون والاختبار
- المطورين المساهمين في المشروع
- المجتمع العراقي للبرمجة والتطوير

---

<div align="center">

**صُنع بـ ❤️ للمعاهد الأهلية العراقية**

[![GitHub stars](https://img.shields.io/github/stars/your-repo/iraqi-institutes-accounting?style=social)](https://github.com/your-repo/iraqi-institutes-accounting/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/your-repo/iraqi-institutes-accounting?style=social)](https://github.com/your-repo/iraqi-institutes-accounting/network)

</div>
