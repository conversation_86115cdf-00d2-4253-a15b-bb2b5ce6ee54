-- نظام محاسبة المعاهد الأهلية العراقية
-- Database Structure Script

-- ============================================
-- جدول إعدادات المعهد
-- ============================================
CREATE TABLE اعدادات_المعهد (
    معرف_الاعداد AUTOINCREMENT PRIMARY KEY,
    اسم_المعهد TEXT(100) NOT NULL,
    عنوان_المعهد TEXT(255),
    رقم_الهاتف TEXT(20),
    البريد_الالكتروني TEXT(100),
    شعار_المعهد OLE,
    نسبة_المعهد_افتراضية DOUBLE DEFAULT 0.7,
    نسبة_المدرس_افتراضية DOUBLE DEFAULT 0.3,
    تاريخ_انشاء_النظام DATETIME DEFAULT Now(),
    ملاحظات MEMO
);

-- ============================================
-- جدول الدورات والمواد
-- ============================================
CREATE TABLE الدورات (
    معرف_الدورة AUTOINCREMENT PRIMARY KEY,
    اسم_الدورة TEXT(100) NOT NULL,
    وصف_الدورة TEXT(255),
    مدة_الدورة INTEGER, -- بالأشهر
    رسوم_الدورة CURRENCY NOT NULL,
    حالة_الدورة TEXT(20) DEFAULT 'نشطة', -- نشطة، متوقفة
    تاريخ_الانشاء DATETIME DEFAULT Now()
);

-- ============================================
-- جدول المدرسين
-- ============================================
CREATE TABLE المدرسين (
    معرف_المدرس AUTOINCREMENT PRIMARY KEY,
    اسم_المدرس TEXT(100) NOT NULL,
    رقم_الهاتف TEXT(20),
    العنوان TEXT(255),
    التخصص TEXT(100),
    نوع_الراتب TEXT(20) DEFAULT 'نسبة', -- نسبة، ثابت، مختلط
    الراتب_الثابت CURRENCY DEFAULT 0,
    نسبة_المدرس DOUBLE DEFAULT 0.3,
    تاريخ_التعيين DATETIME DEFAULT Now(),
    حالة_المدرس TEXT(20) DEFAULT 'نشط', -- نشط، متوقف
    ملاحظات MEMO
);

-- ============================================
-- جدول الطلاب
-- ============================================
CREATE TABLE الطلاب (
    معرف_الطالب AUTOINCREMENT PRIMARY KEY,
    اسم_الطالب TEXT(100) NOT NULL,
    الجنس TEXT(10) DEFAULT 'ذكر', -- ذكر، أنثى
    العمر INTEGER,
    رقم_الهاتف TEXT(20),
    هاتف_ولي_الامر TEXT(20),
    العنوان TEXT(255),
    الصف_الدراسي TEXT(50),
    معرف_الدورة INTEGER,
    معرف_المدرس INTEGER,
    رسوم_الدورة CURRENCY NOT NULL,
    المبلغ_المدفوع CURRENCY DEFAULT 0,
    المبلغ_المتبقي CURRENCY DEFAULT 0,
    نسبة_الخصم DOUBLE DEFAULT 0,
    تاريخ_التسجيل DATETIME DEFAULT Now(),
    حالة_الطالب TEXT(20) DEFAULT 'نشط', -- نشط، منسحب، متخرج
    تاريخ_الانسحاب DATETIME,
    مبلغ_الاسترداد CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    FOREIGN KEY (معرف_الدورة) REFERENCES الدورات(معرف_الدورة),
    FOREIGN KEY (معرف_المدرس) REFERENCES المدرسين(معرف_المدرس)
);

-- ============================================
-- جدول الأقساط المدفوعة
-- ============================================
CREATE TABLE الأقساط_المدفوعة (
    معرف_الدفع AUTOINCREMENT PRIMARY KEY,
    معرف_الطالب INTEGER NOT NULL,
    المبلغ_المدفوع CURRENCY NOT NULL,
    تاريخ_الدفع DATETIME DEFAULT Now(),
    طريقة_الدفع TEXT(20) DEFAULT 'نقد', -- نقد، شيك، تحويل
    رقم_الإيصال TEXT(20),
    الملاحظات TEXT(255),
    FOREIGN KEY (معرف_الطالب) REFERENCES الطلاب(معرف_الطالب)
);

-- ============================================
-- جدول المصاريف
-- ============================================
CREATE TABLE المصاريف (
    معرف_المصروف AUTOINCREMENT PRIMARY KEY,
    نوع_المصروف TEXT(100) NOT NULL,
    المبلغ CURRENCY NOT NULL,
    تاريخ_المصروف DATETIME DEFAULT Now(),
    وصف_المصروف TEXT(255),
    فئة_المصروف TEXT(50) DEFAULT 'عام', -- إيجار، كهرباء، قرطاسية، صيانة، عام
    الشهر INTEGER DEFAULT Month(Now()),
    السنة INTEGER DEFAULT Year(Now()),
    ملاحظات MEMO
);

-- ============================================
-- جدول رواتب المدرسين
-- ============================================
CREATE TABLE رواتب_المدرسين (
    معرف_الراتب AUTOINCREMENT PRIMARY KEY,
    معرف_المدرس INTEGER NOT NULL,
    الشهر INTEGER NOT NULL,
    السنة INTEGER NOT NULL,
    عدد_الطلاب INTEGER DEFAULT 0,
    إجمالي_الأقساط CURRENCY DEFAULT 0,
    نسبة_المدرس DOUBLE DEFAULT 0.3,
    مبلغ_الراتب CURRENCY DEFAULT 0,
    الراتب_الثابت CURRENCY DEFAULT 0,
    المبلغ_الإجمالي CURRENCY DEFAULT 0,
    تاريخ_الدفع DATETIME,
    حالة_الدفع TEXT(20) DEFAULT 'مستحق', -- مستحق، مدفوع
    ملاحظات TEXT(255),
    FOREIGN KEY (معرف_المدرس) REFERENCES المدرسين(معرف_المدرس)
);

-- ============================================
-- جدول الانسحابات
-- ============================================
CREATE TABLE انسحابات_الطلاب (
    معرف_الانسحاب AUTOINCREMENT PRIMARY KEY,
    معرف_الطالب INTEGER NOT NULL,
    تاريخ_الانسحاب DATETIME DEFAULT Now(),
    سبب_الانسحاب TEXT(255),
    المبلغ_المدفوع_إجمالي CURRENCY DEFAULT 0,
    نسبة_الخصم DOUBLE DEFAULT 0,
    المبلغ_المسترد CURRENCY DEFAULT 0,
    المبلغ_المحتفظ_به CURRENCY DEFAULT 0,
    ملاحظات MEMO,
    FOREIGN KEY (معرف_الطالب) REFERENCES الطلاب(معرف_الطالب)
);

-- ============================================
-- بيانات أولية للنظام
-- ============================================

-- إدخال إعدادات المعهد الافتراضية
INSERT INTO اعدادات_المعهد (اسم_المعهد, عنوان_المعهد, رقم_الهاتف, نسبة_المعهد_افتراضية, نسبة_المدرس_افتراضية, ملاحظات)
VALUES ('معهد النور الأهلي', 'بغداد - العراق', '07701234567', 0.7, 0.3, 'إعدادات افتراضية للنظام');

-- إدخال دورات افتراضية
INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, مدة_الدورة, رسوم_الدورة, حالة_الدورة)
VALUES 
('اللغة الإنجليزية - مستوى أول', 'دورة تأسيسية في اللغة الإنجليزية', 3, 150000, 'نشطة'),
('الرياضيات - الثالث متوسط', 'دورة الرياضيات للصف الثالث المتوسط', 6, 200000, 'نشطة'),
('الفيزياء - السادس العلمي', 'دورة الفيزياء للصف السادس العلمي', 8, 250000, 'نشطة'),
('الكيمياء - السادس العلمي', 'دورة الكيمياء للصف السادس العلمي', 8, 250000, 'نشطة'),
('الحاسوب - مبتدئ', 'دورة أساسيات الحاسوب', 2, 100000, 'نشطة');

-- إدخال مصاريف افتراضية
INSERT INTO المصاريف (نوع_المصروف, المبلغ, وصف_المصروف, فئة_المصروف, الشهر, السنة)
VALUES 
('إيجار المبنى', 1000000, 'إيجار شهري للمبنى', 'إيجار', Month(Now()), Year(Now())),
('فاتورة الكهرباء', 200000, 'فاتورة الكهرباء الشهرية', 'كهرباء', Month(Now()), Year(Now())),
('قرطاسية ومستلزمات', 150000, 'أوراق وأقلام ومستلزمات تعليمية', 'قرطاسية', Month(Now()), Year(Now()));