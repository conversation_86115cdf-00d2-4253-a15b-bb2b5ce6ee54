# 🚀 ابدأ هنا - نظام محاسبة المعاهد الأهلية العراقية

## 🎯 خطوات التشغيل السريع

### 1. إنشاء قاعدة البيانات
```
افتح Microsoft Access
↓
أنشئ قاعدة بيانات جديدة
↓
احفظها باسم: نظام_محاسبة_المعهد.accdb
```

### 2. إنشاء الجداول
```
في Access، اذهب إلى SQL View
↓
انسخ محتوى ملف: build_database.sql
↓
شغل الكود لإنشاء الجداول
```

### 3. إضافة البيانات التجريبية
```
في SQL View مرة أخرى
↓
انسخ محتوى ملف: sample_data.sql
↓
شغل الكود لإضافة البيانات التجريبية
```

### 4. إضافة الوحدات النمطية
```
في Access، اذهب إلى Create → Module
↓
انسخ محتوى ملف: VBA_Modules.vba
↓
احفظ الوحدة باسم: الوظائف_العامة
```

### 5. إضافة الوحدات المتقدمة
```
أنشئ وحدة جديدة
↓
انسخ محتوى ملف: Advanced_VBA_Macros.vba
↓
احفظ الوحدة باسم: العمليات_المتقدمة
```

### 6. إنشاء النماذج
```
استخدم محتوى ملف: Forms_Design.xml
↓
و Additional_Forms.xml
↓
لإنشاء النماذج كما هو موضح في الدليل
```

### 7. إنشاء التقارير
```
استخدم محتوى ملف: Reports_Design.xml
↓
و Additional_Reports_Queries.xml
↓
لإنشاء التقارير والاستعلامات
```

## 📁 هيكل المجلدات المطلوبة

```
نظام_محاسبة_المعهد.accdb
├── النسخ_الاحتياطية/ (سيتم إنشاؤها تلقائياً)
├── التصدير/ (سيتم إنشاؤها تلقائياً)
├── التقارير/ (سيتم إنشاؤها تلقائياً)
└── الوثائق/ (اختياري)
```

## 🔧 التحقق من التشغيل

بعد إنشاء قاعدة البيانات، يجب أن تحتوي على:

### ✅ الجداول (8 جداول)
- اعدادات_المعهد
- الدورات
- المدرسين
- الطلاب
- الأقساط_المدفوعة
- المصاريف
- رواتب_المدرسين
- انسحابات_الطلاب

### ✅ الاستعلامات (8 استعلامات)
- استعلام_الطلاب
- استعلام_المدرسين
- استعلام_الأقساط_المستحقة
- استعلام_الأقساط_المدفوعة_اليوم
- استعلام_الأقساط_المدفوعة_الشهرية
- استعلام_المصاريف_الشهرية
- استعلام_الإحصائيات_المالية_الشهرية
- استعلام_أداء_المدرسين

### ✅ النماذج (6 نماذج)
- الواجهة_الرئيسية
- نموذج_الطلاب
- نموذج_المدرسين
- نموذج_الحسابات
- نموذج_دفع_قسط
- نموذج_انسحاب_الطالب

### ✅ التقارير (4 تقارير)
- تقرير_الطلاب_الشامل
- تقرير_الأقساط_المدفوعة
- تقرير_رواتب_المدرسين
- تقرير_إيصال_الدفع

### ✅ الوحدات النمطية (2 وحدات)
- الوظائف_العامة
- العمليات_المتقدمة

## 🎨 البيانات التجريبية

بعد تشغيل sample_data.sql، ستحصل على:

- **1 إعداد معهد** (معهد النور الأهلي)
- **8 دورات** (إنجليزي، رياضيات، فيزياء، كيمياء، حاسوب، أحياء، تاريخ، جغرافيا)
- **8 مدرسين** بتخصصات مختلفة
- **16 طالب** موزعين على الدورات
- **22 قسط مدفوع** لشهري فبراير ومارس
- **11 مصروف** متنوع
- **16 راتب مدرس** لشهرين

## 📞 الدعم الفني

إذا واجهت أي مشكلة:

1. **راجع الدليل المفصل**: `دليل_التثبيت_والتشغيل.md`
2. **راجع الدليل السريع**: `دليل_الاستخدام_السريع.md`
3. **راجع ملخص المشروع**: `ملخص_المشروع.md`

## 🚀 البدء السريع

```bash
# انقر مرتين على هذا الملف لتشغيل النظام
تشغيل_النظام.bat
```

## 📋 قائمة التحقق السريعة

- [ ] تم إنشاء قاعدة البيانات
- [ ] تم إنشاء الجداول
- [ ] تم إضافة البيانات التجريبية
- [ ] تم إضافة الوحدات النمطية
- [ ] تم إنشاء النماذج
- [ ] تم إنشاء التقارير
- [ ] تم اختبار النظام
- [ ] النظام جاهز للاستخدام

---

## 🎉 مبروك! النظام جاهز للاستخدام

بعد إكمال جميع الخطوات، سيكون لديك نظام محاسبة متكامل وجاهز للاستخدام في معهدك!

**تطوير**: Zencoder AI | **الإصدار**: 1.0 | **السنة**: 2024