# 🚀 الدليل الشامل لتشغيل نظام محاسبة المعاهد الأهلية العراقية

## 📋 نظرة عامة

هذا دليل شامل وموحد لإنشاء وتشغيل نظام محاسبة المعاهد الأهلية العراقية من البداية حتى النهاية.

---

## 🎯 المتطلبات الأساسية

### البرامج المطلوبة:
- ✅ Windows 10 أو أحدث
- ✅ Microsoft Access 2016 أو أحدث
- ✅ 4 جيجابايت رام (الحد الأدنى)
- ✅ 500 ميجابايت مساحة فارغة

### الملفات المطلوبة:
- ✅ جميع ملفات المشروع في مجلد `d:/برنامج معهد/`
- ✅ صلاحيات الكتابة في المجلد

---

## 🔧 الخطوة 1: إنشاء قاعدة البيانات

### 1.1 إنشاء قاعدة البيانات الجديدة

```
1. افتح Microsoft Access
2. اختر "Blank Database" (قاعدة بيانات فارغة)
3. اكتب الاسم: نظام_محاسبة_المعهد
4. اختر المجلد: d:/برنامج معهد/
5. اضغط "Create"
```

### 1.2 التحقق من النجاح
- يجب أن تفتح قاعدة بيانات جديدة
- يجب أن تجد جدول "Table1" - أغلقه بدون حفظ

---

## 🗃️ الخطوة 2: إنشاء الجداول

### 2.1 تنفيذ سكريبت الجداول

```
1. اذهب إلى Create → Query Design
2. أغلق نافذة "Show Table"
3. اضغط "SQL View" في شريط الأدوات
4. افتح ملف build_database.sql في Notepad
5. انسخ المحتوى بالكامل (Ctrl+A ثم Ctrl+C)
6. ألصق في نافذة SQL في Access (Ctrl+V)
7. اضغط "Run" (!) في شريط الأدوات
8. إذا ظهرت رسائل تحذير، اضغط "Yes"
```

### 2.2 التحقق من الجداول
يجب أن تجد 8 جداول في Navigation Pane:
- ✅ اعدادات_المعهد
- ✅ الدورات
- ✅ المدرسين
- ✅ الطلاب
- ✅ الأقساط_المدفوعة
- ✅ المصاريف
- ✅ رواتب_المدرسين
- ✅ انسحابات_الطلاب

---

## 📊 الخطوة 3: إضافة البيانات التجريبية

### 3.1 تنفيذ سكريبت البيانات

```
1. أنشئ استعلام جديد (Create → Query Design)
2. أغلق نافذة "Show Table"
3. اضغط "SQL View"
4. افتح ملف sample_data.sql في Notepad
5. انسخ المحتوى بالكامل
6. ألصق في نافذة SQL
7. اضغط "Run" (!)
```

### 3.2 التحقق من البيانات
- ✅ **الطلاب**: 16 طالب
- ✅ **المدرسين**: 8 مدرسين
- ✅ **الدورات**: 8 دورات
- ✅ **الأقساط المدفوعة**: 22 قسط
- ✅ **المصاريف**: 11 مصروف

---

## 🔧 الخطوة 4: إضافة الوحدات النمطية

### 4.1 إنشاء الوحدة الأولى

```
1. اذهب إلى Create → Module
2. امسح أي نص موجود
3. افتح ملف VBA_Modules.vba في Notepad
4. انسخ المحتوى بالكامل
5. ألصق في نافذة VBA
6. اضغط Ctrl+S
7. اكتب الاسم: الوظائف_العامة
8. اضغط OK
```

### 4.2 إنشاء الوحدة الثانية

```
1. أنشئ Module جديد
2. افتح ملف Advanced_VBA_Macros.vba
3. انسخ المحتوى وألصق في VBA
4. احفظ باسم: العمليات_المتقدمة
```

### 4.3 التحقق من الوحدات
في Navigation Pane تحت "Modules":
- ✅ الوظائف_العامة
- ✅ العمليات_المتقدمة

---

## 🔍 الخطوة 5: إنشاء الاستعلامات

### 5.1 قائمة الاستعلامات المطلوبة

من ملف `Additional_Reports_Queries.xml`، أنشئ هذه الاستعلامات:

1. **استعلام_الطلاب**
2. **استعلام_المدرسين**
3. **استعلام_الأقساط_المستحقة**
4. **استعلام_الأقساط_المدفوعة_اليوم**
5. **استعلام_الأقساط_المدفوعة_الشهرية**
6. **استعلام_المصاريف_الشهرية**
7. **استعلام_الإحصائيات_المالية_الشهرية**
8. **استعلام_أداء_المدرسين**

### 5.2 طريقة إنشاء كل استعلام

```
1. Create → Query Design
2. أغلق "Show Table"
3. اضغط "SQL View"
4. ابحث عن الاستعلام في ملف Additional_Reports_Queries.xml
5. انسخ الكود بين <SQL> و </SQL>
6. ألصق في نافذة SQL
7. اضغط "Run" للاختبار
8. احفظ باسم الاستعلام المطلوب
```

---

## 📝 الخطوة 6: إنشاء النماذج

### 6.1 النماذج المطلوبة

من ملف `Forms_Design.xml` و `Additional_Forms.xml`:

1. **الواجهة_الرئيسية** - الشاشة الرئيسية
2. **نموذج_الطلاب** - إدارة الطلاب
3. **نموذج_المدرسين** - إدارة المدرسين
4. **نموذج_الحسابات** - إدارة الحسابات
5. **نموذج_دفع_قسط** - دفع الأقساط
6. **نموذج_انسحاب_الطالب** - انسحاب الطلاب

### 6.2 إرشادات إنشاء النماذج

```
1. Create → Form Design
2. استخدم المعلومات من ملفات XML
3. اضبط الخصائص حسب الجدول المطلوب:
   - Caption: العنوان
   - RecordSource: مصدر البيانات
   - RightToLeft: True (للعربية)
4. أضف العناصر والأزرار المطلوبة
5. احفظ باسم النموذج المطلوب
```

---

## 📄 الخطوة 7: إنشاء التقارير

### 7.1 التقارير المطلوبة

من ملف `Reports_Design.xml`:

1. **تقرير_الطلاب_الشامل**
2. **تقرير_الأقساط_المدفوعة**
3. **تقرير_رواتب_المدرسين**
4. **تقرير_إيصال_الدفع**

### 7.2 إرشادات إنشاء التقارير

```
1. Create → Report Design
2. اضبط RecordSource حسب الاستعلام المطلوب
3. أضف العناصر والحقول المطلوبة
4. اضبط التنسيق للطباعة
5. احفظ باسم التقرير المطلوب
```

---

## 🎯 الخطوة 8: الضبط النهائي

### 8.1 إعدادات قاعدة البيانات

```
1. File → Options
2. Current Database
3. Display Form: الواجهة_الرئيسية
4. اضغط OK
```

### 8.2 إنشاء المجلدات

أنشئ هذه المجلدات في `d:/برنامج معهد/`:
- 📁 النسخ_الاحتياطية
- 📁 التصدير
- 📁 التقارير

### 8.3 تفعيل المحتوى

```
عند فتح قاعدة البيانات:
1. ستظهر رسالة تحذير أمني
2. اضغط "Enable Content"
3. هذا مطلوب لتشغيل كود VBA
```

---

## ✅ الخطوة 9: الاختبار النهائي

### 9.1 اختبار الوظائف الأساسية

```
1. إضافة طالب جديد
2. تسجيل دفع قسط
3. إضافة مدرس جديد
4. تسجيل مصروف
5. طباعة تقرير
6. إنشاء نسخة احتياطية
```

### 9.2 اختبار الحسابات

```
1. حساب المبلغ المتبقي للطالب
2. حساب راتب المدرس
3. حساب الأرباح الشهرية
4. إحصائيات المعهد
```

---

## 🚀 الخطوة 10: البدء في الاستخدام

### 10.1 مسح البيانات التجريبية (اختياري)

```
إذا كنت تريد البدء ببيانات حقيقية:
1. احذف محتوى الجداول (احتفظ بالهيكل)
2. أدخل بيانات معهدك الحقيقية
3. ابدأ الاستخدام العملي
```

### 10.2 التدريب على الاستخدام

```
1. اقرأ دليل_الاستخدام_السريع.md
2. تدرب على العمليات اليومية
3. تعلم العمليات الشهرية
4. تدرب على النسخ الاحتياطي
```

---

## 🔧 حل المشاكل الشائعة

### مشكلة 1: لا يمكن التعرف على تنسيق قاعدة البيانات
```
الحل:
- تأكد من إنشاء قاعدة بيانات جديدة في Access
- لا تنسخ ملفات أو تغير امتدادات
- ابدأ من قاعدة بيانات فارغة
```

### مشكلة 2: خطأ في تشغيل كود SQL
```
الحل:
- تأكد من نسخ الكود بالكامل
- تأكد من عدم وجود أحرف غريبة
- جرب تشغيل الكود على أجزاء صغيرة
```

### مشكلة 3: خطأ في كود VBA
```
الحل:
- تأكد من تفعيل المحتوى (Enable Content)
- تأكد من إعدادات الأمان
- تأكد من إصدار Access (2016 أو أحدث)
```

### مشكلة 4: البيانات العربية لا تظهر
```
الحل:
- تأكد من ضبط خاصية RightToLeft = True
- تأكد من نوع البيانات Text/Short Text
- تأكد من إعدادات اللغة في Windows
```

---

## 📋 قائمة التحقق السريعة

### ✅ تم إنشاء قاعدة البيانات
- [ ] قاعدة بيانات جديدة في Access
- [ ] الاسم: نظام_محاسبة_المعهد.accdb
- [ ] المجلد: d:/برنامج معهد/

### ✅ تم إنشاء الجداول
- [ ] 8 جداول مع الهيكل الصحيح
- [ ] العلاقات بين الجداول
- [ ] البيانات التجريبية (16 طالب، 8 مدرسين)

### ✅ تم إضافة الكود
- [ ] 2 وحدة نمطية مع كود VBA
- [ ] 8 استعلامات تعمل بشكل صحيح
- [ ] تفعيل المحتوى (Enable Content)

### ✅ تم إنشاء الواجهات
- [ ] 6 نماذج على الأقل
- [ ] 4 تقارير على الأقل
- [ ] الواجهة الرئيسية كنموذج البداية

### ✅ النظام جاهز للاستخدام
- [ ] تم اختبار جميع الوظائف
- [ ] تم إنشاء نسخة احتياطية
- [ ] تم التدريب على الاستخدام

---

## 🎉 مبروك! النظام جاهز

إذا أكملت جميع الخطوات بنجاح، فلديك الآن:

### 🏆 نظام محاسبة متكامل يشمل:
- ✅ **إدارة الطلاب والمدرسين**
- ✅ **نظام الأقساط والمصاريف**
- ✅ **حساب الرواتب والأرباح**
- ✅ **التقارير والإحصائيات**
- ✅ **واجهة عربية احترافية**
- ✅ **نسخ احتياطي وأمان**

---

## 📞 للحصول على المساعدة

### الملفات المرجعية:
- `دليل_الاستخدام_السريع.md` - للاستخدام اليومي
- `ملخص_المشروع.md` - لفهم النظام
- `README.md` - معلومات عامة

### ملفات الكود:
- `build_database.sql` - هيكل الجداول
- `sample_data.sql` - البيانات التجريبية
- `VBA_Modules.vba` - الوظائف الأساسية
- `Advanced_VBA_Macros.vba` - الوظائف المتقدمة

---

## 🚀 ابدأ الآن!

**الخطوة التالية:** افتح Microsoft Access وابدأ بالخطوة الأولى من هذا الدليل.

**بالتوفيق! 🎯**

---

**تطوير:** Zencoder AI | **الإصدار:** 1.0 | **2024**