' ===============================================
' نظام محاسبة المعاهد الأهلية العراقية
' Iraqi Private Institutes Accounting System
' Enhanced VBA Modules and Functions
' ===============================================

Option Compare Database
Option Explicit

' ===============================================
' Module: MainFunctions
' الوظائف الرئيسية للنظام
' ===============================================

' Function to calculate teacher earnings for a specific month
Public Sub CalculateTeacherEarnings(TeacherID As Long, Optional TargetMonth As Integer = 0, Optional TargetYear As Integer = 0)
    Dim db As Database
    Dim rs As Recordset
    Dim rsTeacher As Recordset
    Dim rsEarnings As Recordset
    Dim strSQL As String
    Dim lngMonth As Integer
    Dim lngYear As Integer
    Dim dblTotalFees As Double
    Dim dblTeacherShare As Double
    Dim dblInstituteShare As Double
    Dim intStudentCount As Integer
    Dim dblSharePercentage As Double
    Dim strSalaryType As String
    Dim dblMonthlySalary As Double
    
    ' Use current month/year if not specified
    If TargetMonth = 0 Then lngMonth = Month(Date) Else lngMonth = TargetMonth
    If TargetYear = 0 Then lngYear = Year(Date) Else lngYear = TargetYear
    
    Set db = CurrentDb
    
    ' Get teacher information
    strSQL = "SELECT SalaryType, SharePercentage, MonthlySalary FROM Teachers WHERE TeacherID = " & TeacherID
    Set rsTeacher = db.OpenRecordset(strSQL)
    
    If rsTeacher.EOF Then
        MsgBox "المدرس غير موجود", vbError
        Exit Sub
    End If
    
    strSalaryType = Nz(rsTeacher!SalaryType, "Percentage")
    dblSharePercentage = Nz(rsTeacher!SharePercentage, 30)
    dblMonthlySalary = Nz(rsTeacher!MonthlySalary, 0)
    rsTeacher.Close
    
    ' Calculate total fees from active students for this teacher
    strSQL = "SELECT COUNT(*) AS StudentCount, SUM(TotalFee) AS TotalFees " & _
             "FROM Students WHERE TeacherID = " & TeacherID & " AND StudentStatus = 'Active'"
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF Then
        intStudentCount = Nz(rs!StudentCount, 0)
        dblTotalFees = Nz(rs!TotalFees, 0)
    End If
    rs.Close
    
    ' Calculate shares based on salary type
    If strSalaryType = "Fixed" Then
        dblTeacherShare = dblMonthlySalary
        dblInstituteShare = dblTotalFees - dblMonthlySalary
    Else
        dblTeacherShare = dblTotalFees * (dblSharePercentage / 100)
        dblInstituteShare = dblTotalFees - dblTeacherShare
    End If
    
    ' Check if earnings record already exists
    strSQL = "SELECT * FROM TeacherEarnings WHERE TeacherID = " & TeacherID & _
             " AND Month = " & lngMonth & " AND Year = " & lngYear
    Set rsEarnings = db.OpenRecordset(strSQL, dbOpenDynaset)
    
    If rsEarnings.EOF Then
        ' Create new record
        rsEarnings.AddNew
        rsEarnings!TeacherID = TeacherID
        rsEarnings!Month = lngMonth
        rsEarnings!Year = lngYear
    End If
    
    ' Update earnings data
    rsEarnings!StudentCount = intStudentCount
    rsEarnings!TotalStudentFees = dblTotalFees
    rsEarnings!TeacherShare = dblTeacherShare
    rsEarnings!InstituteShare = dblInstituteShare
    rsEarnings!IsPaid = False
    
    rsEarnings.Update
    rsEarnings.Close
    
    Set db = Nothing
End Sub

' Function to process student withdrawal
Public Sub ProcessStudentWithdrawal(StudentID As Long, WithdrawalDate As Date, Reason As String, RefundPercentage As Double)
    Dim db As Database
    Dim rs As Recordset
    Dim strSQL As String
    Dim dblTotalFee As Double
    Dim dblPaidAmount As Double
    Dim dblRefundAmount As Double
    
    Set db = CurrentDb
    
    ' Get student information
    strSQL = "SELECT TotalFee, PaidAmount FROM Students WHERE StudentID = " & StudentID
    Set rs = db.OpenRecordset(strSQL)
    
    If rs.EOF Then
        MsgBox "الطالب غير موجود", vbError
        Exit Sub
    End If
    
    dblTotalFee = Nz(rs!TotalFee, 0)
    dblPaidAmount = Nz(rs!PaidAmount, 0)
    rs.Close
    
    ' Calculate refund amount
    dblRefundAmount = dblPaidAmount * (RefundPercentage / 100)
    
    ' Update student status
    strSQL = "UPDATE Students SET StudentStatus = 'Withdrawn', WithdrawalDate = #" & _
             Format(WithdrawalDate, "mm/dd/yyyy") & "#, RefundAmount = " & dblRefundAmount & _
             " WHERE StudentID = " & StudentID
    db.Execute strSQL
    
    ' Create withdrawal record
    strSQL = "INSERT INTO Withdrawals (StudentID, WithdrawalDate, Reason, RefundAmount, RefundPercentage) " & _
             "VALUES (" & StudentID & ", #" & Format(WithdrawalDate, "mm/dd/yyyy") & "#, '" & _
             Replace(Reason, "'", "''") & "', " & dblRefundAmount & ", " & RefundPercentage & ")"
    db.Execute strSQL
    
    Set db = Nothing
    
    MsgBox "تم تسجيل انسحاب الطالب بنجاح" & vbCrLf & _
           "المبلغ المسترجع: " & Format(dblRefundAmount, "Currency"), vbInformation
End Sub

' Function to backup database
Public Sub BackupDatabase()
    Dim strBackupPath As String
    Dim strCurrentPath As String
    Dim strBackupName As String
    
    strCurrentPath = CurrentDb.Name
    strBackupName = "InstituteAccounting_Backup_" & Format(Date, "yyyy_mm_dd") & ".accdb"
    strBackupPath = Left(strCurrentPath, InStrRev(strCurrentPath, "\")) & strBackupName
    
    On Error GoTo ErrorHandler
    
    ' Close current database connections
    DoCmd.RunCommand acCmdCompactDatabase
    
    ' Copy file
    FileCopy strCurrentPath, strBackupPath
    
    MsgBox "تم إنشاء النسخة الاحتياطية بنجاح" & vbCrLf & _
           "المسار: " & strBackupPath, vbInformation
    
    Exit Sub
    
ErrorHandler:
    MsgBox "خطأ في إنشاء النسخة الاحتياطية: " & Err.Description, vbError
End Sub

' Function to apply date filter on payments form
Public Sub ApplyDateFilter()
    Dim frm As Form
    Dim strFilter As String
    
    Set frm = Forms("PaymentsForm")
    
    strFilter = ""
    
    If Not IsNull(frm.txt_DateFrom) And Not IsNull(frm.txt_DateTo) Then
        strFilter = "PaymentDate BETWEEN #" & Format(frm.txt_DateFrom, "mm/dd/yyyy") & _
                   "# AND #" & Format(frm.txt_DateTo, "mm/dd/yyyy") & "#"
    ElseIf Not IsNull(frm.txt_DateFrom) Then
        strFilter = "PaymentDate >= #" & Format(frm.txt_DateFrom, "mm/dd/yyyy") & "#"
    ElseIf Not IsNull(frm.txt_DateTo) Then
        strFilter = "PaymentDate <= #" & Format(frm.txt_DateTo, "mm/dd/yyyy") & "#"
    End If
    
    If strFilter <> "" Then
        frm.Filter = strFilter
        frm.FilterOn = True
    Else
        frm.FilterOn = False
    End If
End Sub

' Function to update student amounts after payment
Public Sub UpdateStudentAmounts(StudentID As Long, PaymentAmount As Double)
    Dim db As Database
    Dim strSQL As String
    
    Set db = CurrentDb
    
    strSQL = "UPDATE Students SET " & _
             "PaidAmount = PaidAmount + " & PaymentAmount & ", " & _
             "RemainingAmount = TotalFee - (PaidAmount + " & PaymentAmount & ") " & _
             "WHERE StudentID = " & StudentID
    
    db.Execute strSQL
    
    Set db = Nothing
End Sub

' Function to generate receipt number
Public Function GenerateReceiptNumber() As String
    Dim db As Database
    Dim rs As Recordset
    Dim strSQL As String
    Dim lngLastNumber As Long
    
    Set db = CurrentDb
    
    strSQL = "SELECT MAX(Val(Mid(ReceiptNumber, 4))) AS LastNumber FROM Payments " & _
             "WHERE ReceiptNumber LIKE 'REC*' AND Year(PaymentDate) = " & Year(Date)
    
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF And Not IsNull(rs!LastNumber) Then
        lngLastNumber = rs!LastNumber + 1
    Else
        lngLastNumber = 1
    End If
    
    rs.Close
    Set db = Nothing
    
    GenerateReceiptNumber = "REC" & Format(lngLastNumber, "0000")
End Function

' Function to validate required fields
Public Function ValidateRequiredFields(frm As Form, FieldNames As String) As Boolean
    Dim arrFields() As String
    Dim i As Integer
    Dim strMissingFields As String
    
    arrFields = Split(FieldNames, ",")
    
    For i = 0 To UBound(arrFields)
        If IsNull(frm.Controls(Trim(arrFields(i))).Value) Or _
           frm.Controls(Trim(arrFields(i))).Value = "" Then
            strMissingFields = strMissingFields & "- " & Trim(arrFields(i)) & vbCrLf
        End If
    Next i
    
    If strMissingFields <> "" Then
        MsgBox "يرجى ملء الحقول المطلوبة التالية:" & vbCrLf & strMissingFields, vbExclamation
        ValidateRequiredFields = False
    Else
        ValidateRequiredFields = True
    End If
End Function

' Function to format currency in Arabic
Public Function FormatCurrencyArabic(Amount As Double) As String
    FormatCurrencyArabic = Format(Amount, "#,##0") & " دينار عراقي"
End Function

' Function to get institute settings
Public Function GetInstituteSetting(SettingName As String) As Variant
    Dim db As Database
    Dim rs As Recordset
    Dim strSQL As String
    
    Set db = CurrentDb
    
    strSQL = "SELECT " & SettingName & " FROM InstituteSettings WHERE SettingID = 1"
    Set rs = db.OpenRecordset(strSQL)
    
    If Not rs.EOF Then
        GetInstituteSetting = rs(SettingName)
    Else
        GetInstituteSetting = Null
    End If
    
    rs.Close
    Set db = Nothing
End Function

' Function to update institute setting
Public Sub UpdateInstituteSetting(SettingName As String, SettingValue As Variant)
    Dim db As Database
    Dim strSQL As String
    
    Set db = CurrentDb
    
    If IsNumeric(SettingValue) Then
        strSQL = "UPDATE InstituteSettings SET " & SettingName & " = " & SettingValue & " WHERE SettingID = 1"
    Else
        strSQL = "UPDATE InstituteSettings SET " & SettingName & " = '" & Replace(SettingValue, "'", "''") & "' WHERE SettingID = 1"
    End If
    
    db.Execute strSQL
    
    Set db = Nothing
End Sub

' Function to calculate monthly profit
Public Function CalculateMonthlyProfit(TargetMonth As Integer, TargetYear As Integer) As Double
    Dim db As Database
    Dim rs As Recordset
    Dim strSQL As String
    Dim dblTotalRevenue As Double
    Dim dblTotalExpenses As Double
    Dim dblTeacherShares As Double
    
    Set db = CurrentDb
    
    ' Calculate total revenue for the month
    strSQL = "SELECT SUM(AmountPaid) AS TotalRevenue FROM Payments " & _
             "WHERE Month(PaymentDate) = " & TargetMonth & " AND Year(PaymentDate) = " & TargetYear
    Set rs = db.OpenRecordset(strSQL)
    If Not rs.EOF Then dblTotalRevenue = Nz(rs!TotalRevenue, 0)
    rs.Close
    
    ' Calculate total expenses for the month
    strSQL = "SELECT SUM(Amount) AS TotalExpenses FROM Expenses " & _
             "WHERE Month(ExpenseDate) = " & TargetMonth & " AND Year(ExpenseDate) = " & TargetYear
    Set rs = db.OpenRecordset(strSQL)
    If Not rs.EOF Then dblTotalExpenses = Nz(rs!TotalExpenses, 0)
    rs.Close
    
    ' Calculate teacher shares for the month
    strSQL = "SELECT SUM(TeacherShare) AS TotalTeacherShares FROM TeacherEarnings " & _
             "WHERE Month = " & TargetMonth & " AND Year = " & TargetYear
    Set rs = db.OpenRecordset(strSQL)
    If Not rs.EOF Then dblTeacherShares = Nz(rs!TotalTeacherShares, 0)
    rs.Close
    
    Set db = Nothing
    
    CalculateMonthlyProfit = dblTotalRevenue - dblTotalExpenses - dblTeacherShares
End Function

' ===============================================
' Auto-execution functions
' ===============================================

' Function to run on database startup
Public Sub AutoExec()
    ' Set default form
    DoCmd.OpenForm "MainDashboard"
    
    ' Hide navigation pane
    DoCmd.NavigateTo "acNavigationCategoryObjectType"
    DoCmd.RunCommand acCmdWindowHide
    
    ' Set application title
    Application.SetOption "Application Title", "نظام محاسبة المعاهد الأهلية العراقية"
End Sub

' Function to initialize new academic year
Public Sub InitializeNewAcademicYear()
    Dim db As Database
    Dim intResponse As Integer
    
    intResponse = MsgBox("هل تريد بدء سنة دراسية جديدة؟" & vbCrLf & _
                        "سيتم أرشفة البيانات الحالية وإعداد النظام للسنة الجديدة.", _
                        vbYesNo + vbQuestion, "سنة دراسية جديدة")
    
    If intResponse = vbYes Then
        Set db = CurrentDb
        
        ' Archive current data (optional - create archive tables)
        ' Reset student statuses if needed
        ' Clear temporary data
        
        MsgBox "تم إعداد النظام للسنة الدراسية الجديدة بنجاح", vbInformation
        
        Set db = Nothing
    End If
End Sub
