-- بيانات تجريبية لنظام محاسبة المعاهد الأهلية العراقية

-- إدراج بيانات إعدادات المعهد
INSERT INTO اعدادات_المعهد (
    اسم_المعهد, 
    عنوان_المعهد, 
    رقم_الهاتف, 
    البريد_الالكتروني,
    نسبة_المعهد_افتراضية,
    نسبة_المدرس_افتراضية,
    تاريخ_انشاء_النظام,
    ملاحظات
) VALUES (
    'معهد النور الأهلي',
    'بغداد - الكرادة - شارع الربيع',
    '07701234567',
    '<EMAIL>',
    0.7,
    0.3,
    #2024-01-01#,
    'معهد أهلي للتعليم المساعد'
);

-- إدراج بيانات الدورات
INSERT INTO الدورات (اسم_الدورة, وصف_الدورة, مدة_الدورة, رسوم_الدورة, حالة_الدورة, تاريخ_الانشاء)
VALUES 
('اللغة الإنجليزية - المستوى الأول', 'دورة تعليم اللغة الإنجليزية للمبتدئين', 3, 150000, 'نشط', #2024-01-15#),
('الرياضيات - الثالث متوسط', 'دورة الرياضيات للصف الثالث المتوسط', 4, 200000, 'نشط', #2024-01-15#),
('الفيزياء - السادس العلمي', 'دورة الفيزياء للصف السادس العلمي', 4, 250000, 'نشط', #2024-01-15#),
('الكيمياء - السادس العلمي', 'دورة الكيمياء للصف السادس العلمي', 4, 250000, 'نشط', #2024-01-15#),
('الحاسوب - مبتدئ', 'دورة تعليم الحاسوب للمبتدئين', 2, 100000, 'نشط', #2024-01-15#),
('الأحياء - الخامس العلمي', 'دورة الأحياء للصف الخامس العلمي', 4, 220000, 'نشط', #2024-01-15#),
('التاريخ - السادس الأدبي', 'دورة التاريخ للصف السادس الأدبي', 3, 180000, 'نشط', #2024-01-15#),
('الجغرافيا - السادس الأدبي', 'دورة الجغرافيا للصف السادس الأدبي', 3, 180000, 'نشط', #2024-01-15#);

-- إدراج بيانات المدرسين
INSERT INTO المدرسين (اسم_المدرس, رقم_الهاتف, العنوان, التخصص, نوع_الراتب, الراتب_الثابت, نسبة_المدرس, تاريخ_التعيين, حالة_المدرس, ملاحظات)
VALUES
('د. أحمد محمد الفراتي', '07701111111', 'بغداد - الكرادة', 'اللغة الإنجليزية', 'نسبة', 0, 0.35, #2024-01-15#, 'نشط', 'مدرس خبير في اللغة الإنجليزية'),
('أ. فاطمة علي حسين', '07702222222', 'بغداد - الجادرية', 'الرياضيات', 'نسبة', 0, 0.3, #2024-01-15#, 'نشط', 'مدرسة متمكنة في الرياضيات'),
('أ. عمر سعد الدين', '07703333333', 'بغداد - الكاظمية', 'الفيزياء', 'مختلط', 300000, 0.25, #2024-01-15#, 'نشط', 'مدرس فيزياء خبير'),
('أ. نور الهدى محمد', '07704444444', 'بغداد - الأعظمية', 'الكيمياء', 'نسبة', 0, 0.32, #2024-01-15#, 'نشط', 'مدرسة كيمياء مختصة'),
('أ. حسام الدين عبد الله', '07705555555', 'بغداد - الدورة', 'الحاسوب', 'ثابت', 400000, 0, #2024-01-15#, 'نشط', 'مدرس حاسوب'),
('أ. رانيا طارق', '07706666666', 'بغداد - الكرادة', 'الأحياء', 'نسبة', 0, 0.28, #2024-01-15#, 'نشط', 'مدرسة أحياء'),
('أ. خالد محمود', '07707777777', 'بغداد - الرصافة', 'التاريخ', 'نسبة', 0, 0.25, #2024-01-15#, 'نشط', 'مدرس تاريخ'),
('أ. سارة أحمد', '07708888888', 'بغداد - الكرخ', 'الجغرافيا', 'نسبة', 0, 0.25, #2024-01-15#, 'نشط', 'مدرسة جغرافيا');

-- إدراج بيانات الطلاب (عينة)
INSERT INTO الطلاب (اسم_الطالب, الجنس, العمر, رقم_الهاتف, هاتف_ولي_الامر, العنوان, الصف_الدراسي, معرف_الدورة, معرف_المدرس, رسوم_الدورة, المبلغ_المدفوع, المبلغ_المتبقي, نسبة_الخصم, تاريخ_التسجيل, حالة_الطالب, ملاحظات)
VALUES
-- طلاب دورة اللغة الإنجليزية
('علي محمد حسين', 'ذكر', 16, '07711111111', '07711111112', 'بغداد - الكرادة', 'الأول متوسط', 1, 1, 150000, 100000, 50000, 0, #2024-02-01#, 'نشط', 'طالب مجتهد'),
('فاطمة أحمد علي', 'أنثى', 15, '07712222222', '07712222223', 'بغداد - الكاظمية', 'الثاني متوسط', 1, 1, 150000, 150000, 0, 0, #2024-02-01#, 'نشط', 'طالبة متفوقة'),
('محمد عبد الله', 'ذكر', 14, '07713333333', '07713333334', 'بغداد - الأعظمية', 'الأول متوسط', 1, 1, 150000, 75000, 75000, 0, #2024-02-05#, 'نشط', ''),
('زينب حسن', 'أنثى', 16, '07714444444', '07714444445', 'بغداد - الجادرية', 'الثاني متوسط', 1, 1, 150000, 50000, 100000, 0, #2024-02-10#, 'نشط', ''),

-- طلاب دورة الرياضيات
('أحمد سعد', 'ذكر', 15, '07721111111', '07721111112', 'بغداد - الدورة', 'الثالث متوسط', 2, 2, 200000, 150000, 50000, 0, #2024-02-01#, 'نشط', 'طالب نشيط'),
('نور الهدى', 'أنثى', 15, '07722222222', '07722222223', 'بغداد - الكرخ', 'الثالث متوسط', 2, 2, 200000, 200000, 0, 0, #2024-02-01#, 'نشط', 'طالبة متميزة'),
('حسام محمد', 'ذكر', 14, '07723333333', '07723333334', 'بغداد - الرصافة', 'الثالث متوسط', 2, 2, 200000, 100000, 100000, 0, #2024-02-03#, 'نشط', ''),

-- طلاب دورة الفيزياء
('عمر الفراتي', 'ذكر', 18, '07731111111', '07731111112', 'بغداد - الكرادة', 'السادس العلمي', 3, 3, 250000, 200000, 50000, 0, #2024-02-01#, 'نشط', 'طالب متفوق'),
('رانيا حسين', 'أنثى', 17, '07732222222', '07732222223', 'بغداد - الكاظمية', 'السادس العلمي', 3, 3, 250000, 250000, 0, 0, #2024-02-01#, 'نشط', 'طالبة ممتازة'),
('خالد عبد الله', 'ذكر', 18, '07733333333', '07733333334', 'بغداد - الأعظمية', 'السادس العلمي', 3, 3, 250000, 125000, 125000, 0, #2024-02-02#, 'نشط', ''),

-- طلاب دورة الكيمياء
('سارة أحمد', 'أنثى', 17, '07741111111', '07741111112', 'بغداد - الجادرية', 'السادس العلمي', 4, 4, 250000, 200000, 50000, 0, #2024-02-01#, 'نشط', 'طالبة مجتهدة'),
('حسن علي', 'ذكر', 18, '07742222222', '07742222223', 'بغداد - الدورة', 'السادس العلمي', 4, 4, 250000, 100000, 150000, 0, #2024-02-05#, 'نشط', ''),

-- طلاب دورة الحاسوب
('مريم محمد', 'أنثى', 16, '07751111111', '07751111112', 'بغداد - الكرخ', 'الرابع العلمي', 5, 5, 100000, 100000, 0, 0, #2024-02-01#, 'نشط', 'طالبة مهتمة بالحاسوب'),
('يوسف حسام', 'ذكر', 15, '07752222222', '07752222223', 'بغداد - الرصافة', 'الرابع العلمي', 5, 5, 100000, 50000, 50000, 0, #2024-02-10#, 'نشط', ''),

-- طلاب دورة الأحياء
('هدى سعد', 'أنثى', 17, '07761111111', '07761111112', 'بغداد - الكرادة', 'الخامس العلمي', 6, 6, 220000, 150000, 70000, 0, #2024-02-01#, 'نشط', 'طالبة متميزة'),
('عباس محمد', 'ذكر', 16, '07762222222', '07762222223', 'بغداد - الكاظمية', 'الخامس العلمي', 6, 6, 220000, 110000, 110000, 0, #2024-02-05#, 'نشط', '');

-- إدراج بيانات الأقساط المدفوعة
INSERT INTO الأقساط_المدفوعة (معرف_الطالب, المبلغ_المدفوع, تاريخ_الدفع, طريقة_الدفع, رقم_الإيصال, الملاحظات)
VALUES
-- دفعات شهر فبراير
(1, 100000, #2024-02-01#, 'نقد', '1001', 'دفعة أولى'),
(2, 150000, #2024-02-01#, 'نقد', '1002', 'دفعة كاملة'),
(3, 75000, #2024-02-05#, 'نقد', '1003', 'دفعة أولى'),
(4, 50000, #2024-02-10#, 'نقد', '1004', 'دفعة أولى'),
(5, 100000, #2024-02-01#, 'نقد', '1005', 'دفعة أولى'),
(6, 150000, #2024-02-01#, 'نقد', '1006', 'دفعة أولى'),
(7, 100000, #2024-02-03#, 'نقد', '1007', 'دفعة أولى'),
(8, 150000, #2024-02-01#, 'نقد', '1008', 'دفعة أولى'),
(9, 200000, #2024-02-01#, 'نقد', '1009', 'دفعة أولى'),
(10, 125000, #2024-02-02#, 'نقد', '1010', 'دفعة أولى'),
(11, 150000, #2024-02-01#, 'نقد', '1011', 'دفعة أولى'),
(12, 100000, #2024-02-05#, 'نقد', '1012', 'دفعة أولى'),
(13, 100000, #2024-02-01#, 'نقد', '1013', 'دفعة كاملة'),
(14, 50000, #2024-02-10#, 'نقد', '1014', 'دفعة أولى'),
(15, 100000, #2024-02-01#, 'نقد', '1015', 'دفعة أولى'),
(16, 110000, #2024-02-05#, 'نقد', '1016', 'دفعة أولى'),

-- دفعات شهر مارس
(5, 50000, #2024-03-01#, 'نقد', '1017', 'دفعة ثانية'),
(6, 50000, #2024-03-01#, 'نقد', '1018', 'دفعة ثانية'),
(8, 50000, #2024-03-05#, 'نقد', '1019', 'دفعة ثانية'),
(9, 50000, #2024-03-05#, 'نقد', '1020', 'دفعة ثانية'),
(11, 50000, #2024-03-10#, 'نقد', '1021', 'دفعة ثانية'),
(15, 50000, #2024-03-10#, 'نقد', '1022', 'دفعة ثانية');

-- إدراج بيانات المصاريف
INSERT INTO المصاريف (نوع_المصروف, المبلغ, تاريخ_المصروف, وصف_المصروف, فئة_المصروف, الشهر, السنة, ملاحظات)
VALUES
-- مصاريف شهر فبراير 2024
('إيجار المبنى', 1000000, #2024-02-01#, 'إيجار شهر فبراير', 'إيجار', 2, 2024, 'إيجار شهري'),
('فاتورة الكهرباء', 350000, #2024-02-05#, 'فاتورة كهرباء شهر يناير', 'مرافق', 2, 2024, 'فاتورة شهرية'),
('فاتورة الماء', 80000, #2024-02-05#, 'فاتورة ماء شهر يناير', 'مرافق', 2, 2024, 'فاتورة شهرية'),
('قرطاسية ولوازم', 120000, #2024-02-10#, 'شراء قرطاسية للمعهد', 'مستلزمات', 2, 2024, 'أوراق وأقلام'),
('صيانة الأجهزة', 200000, #2024-02-15#, 'صيانة أجهزة الحاسوب', 'صيانة', 2, 2024, 'صيانة دورية'),
('تنظيف المعهد', 150000, #2024-02-20#, 'تنظيف عام للمعهد', 'تنظيف', 2, 2024, 'تنظيف شهري'),

-- مصاريف شهر مارس 2024
('إيجار المبنى', 1000000, #2024-03-01#, 'إيجار شهر مارس', 'إيجار', 3, 2024, 'إيجار شهري'),
('فاتورة الكهرباء', 380000, #2024-03-05#, 'فاتورة كهرباء شهر فبراير', 'مرافق', 3, 2024, 'فاتورة شهرية'),
('فاتورة الماء', 85000, #2024-03-05#, 'فاتورة ماء شهر فبراير', 'مرافق', 3, 2024, 'فاتورة شهرية'),
('إعلانات', 250000, #2024-03-10#, 'إعلانات في الصحف المحلية', 'تسويق', 3, 2024, 'إعلانات شهرية'),
('مواد تعليمية', 180000, #2024-03-15#, 'شراء كتب ومواد تعليمية', 'مستلزمات', 3, 2024, 'كتب مرجعية');

-- إدراج بيانات رواتب المدرسين (شهر فبراير)
INSERT INTO رواتب_المدرسين (معرف_المدرس, الشهر, السنة, عدد_الطلاب, إجمالي_الأقساط, نسبة_المدرس, مبلغ_الراتب, الراتب_الثابت, المبلغ_الإجمالي, حالة_الدفع, ملاحظات)
VALUES
-- رواتب شهر فبراير 2024
(1, 2, 2024, 4, 375000, 0.35, 131250, 0, 131250, 'مدفوع', 'راتب شهر فبراير'),
(2, 2, 2024, 3, 350000, 0.30, 105000, 0, 105000, 'مدفوع', 'راتب شهر فبراير'),
(3, 2, 2024, 3, 475000, 0.25, 118750, 300000, 418750, 'مدفوع', 'راتب شهر فبراير'),
(4, 2, 2024, 2, 250000, 0.32, 80000, 0, 80000, 'مدفوع', 'راتب شهر فبراير'),
(5, 2, 2024, 2, 150000, 0, 0, 400000, 400000, 'مدفوع', 'راتب شهر فبراير'),
(6, 2, 2024, 2, 210000, 0.28, 58800, 0, 58800, 'مدفوع', 'راتب شهر فبراير'),
(7, 2, 2024, 0, 0, 0.25, 0, 0, 0, 'غير_مستحق', 'لا يوجد طلاب'),
(8, 2, 2024, 0, 0, 0.25, 0, 0, 0, 'غير_مستحق', 'لا يوجد طلاب');

-- إدراج بيانات رواتب المدرسين (شهر مارس)
INSERT INTO رواتب_المدرسين (معرف_المدرس, الشهر, السنة, عدد_الطلاب, إجمالي_الأقساط, نسبة_المدرس, مبلغ_الراتب, الراتب_الثابت, المبلغ_الإجمالي, حالة_الدفع, ملاحظات)
VALUES
-- رواتب شهر مارس 2024
(1, 3, 2024, 4, 0, 0.35, 0, 0, 0, 'غير_مستحق', 'لا توجد أقساط مدفوعة'),
(2, 3, 2024, 3, 100000, 0.30, 30000, 0, 30000, 'مستحق', 'راتب شهر مارس'),
(3, 3, 2024, 3, 100000, 0.25, 25000, 300000, 325000, 'مستحق', 'راتب شهر مارس'),
(4, 3, 2024, 2, 0, 0.32, 0, 0, 0, 'غير_مستحق', 'لا توجد أقساط مدفوعة'),
(5, 3, 2024, 2, 0, 0, 0, 400000, 400000, 'مستحق', 'راتب شهر مارس'),
(6, 3, 2024, 2, 50000, 0.28, 14000, 0, 14000, 'مستحق', 'راتب شهر مارس'),
(7, 3, 2024, 0, 0, 0.25, 0, 0, 0, 'غير_مستحق', 'لا يوجد طلاب'),
(8, 3, 2024, 0, 0, 0.25, 0, 0, 0, 'غير_مستحق', 'لا يوجد طلاب');