<?xml version="1.0" encoding="UTF-8"?>
<!-- تصميم التقارير المحسن لنظام محاسبة المعاهد الأهلية العراقية -->
<!-- Enhanced Reports Design for Iraqi Private Institutes Accounting System -->

<AccessReportsDesign>
  
  <!-- =============================================== -->
  <!-- تقرير الطلاب الشامل -->
  <!-- =============================================== -->
  <Report name="StudentsComprehensiveReport">
    <Properties>
      <Caption>تقرير الطلاب الشامل</Caption>
      <RecordSource>
        SELECT S.StudentID, S.StudentName, S.Gender, S.Age, S.Grade, 
               C.CourseName, T.TeacherName, S.<PERSON>F<PERSON>, S.PaidAmount, 
               S.RemainingAmount, S.RegistrationDate, S.StudentStatus,
               S.ParentName, S.ParentPhone
        FROM (Students S 
        LEFT JOIN Courses C ON S.CourseID = C.CourseID) 
        LEFT JOIN Teachers T ON S.TeacherID = T.TeacherID
        ORDER BY S.StudentName
      </RecordSource>
      <Width>11000</Width>
      <Height>8000</Height>
      <RightToLeft>True</RightToLeft>
    </Properties>
    
    <Sections>
      <!-- Report Header -->
      <ReportHeader>
        <Height>1500</Height>
        <BackColor>#2C3E50</BackColor>
        
        <Controls>
          <Label name="lbl_ReportTitle">
            <Properties>
              <Left>500</Left>
              <Top>300</Top>
              <Width>10000</Width>
              <Height>600</Height>
              <Caption>تقرير الطلاب الشامل</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>24</FontSize>
              <FontWeight>Bold</FontWeight>
              <ForeColor>#FFFFFF</ForeColor>
              <BackStyle>Transparent</BackStyle>
              <TextAlign>Center</TextAlign>
            </Properties>
          </Label>
          
          <Label name="lbl_InstituteName">
            <Properties>
              <Left>500</Left>
              <Top>900</Top>
              <Width>10000</Width>
              <Height>400</Height>
              <Caption>=GetInstituteSetting("InstituteName")</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>16</FontSize>
              <ForeColor>#FFFFFF</ForeColor>
              <BackStyle>Transparent</BackStyle>
              <TextAlign>Center</TextAlign>
            </Properties>
          </Label>
          
          <Label name="lbl_ReportDate">
            <Properties>
              <Left>8500</Left>
              <Top>100</Top>
              <Width>2000</Width>
              <Height>300</Height>
              <Caption>=Format(Date(),"dd/mm/yyyy")</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <ForeColor>#FFFFFF</ForeColor>
              <BackStyle>Transparent</BackStyle>
              <TextAlign>Left</TextAlign>
            </Properties>
          </Label>
        </Controls>
      </ReportHeader>
      
      <!-- Page Header -->
      <PageHeader>
        <Height>800</Height>
        <BackColor>#ECF0F1</BackColor>
        
        <Controls>
          <!-- Column Headers -->
          <Label name="lbl_StudentID_Header">
            <Properties>
              <Left>200</Left>
              <Top>200</Top>
              <Width>600</Width>
              <Height>400</Height>
              <Caption>رقم الطالب</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_StudentName_Header">
            <Properties>
              <Left>800</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>400</Height>
              <Caption>اسم الطالب</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_Course_Header">
            <Properties>
              <Left>2300</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <Caption>الدورة</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_Teacher_Header">
            <Properties>
              <Left>3500</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <Caption>المدرس</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_TotalFee_Header">
            <Properties>
              <Left>4700</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>القسط الكلي</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_PaidAmount_Header">
            <Properties>
              <Left>5700</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>المبلغ المدفوع</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_RemainingAmount_Header">
            <Properties>
              <Left>6700</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>المبلغ المتبقي</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_Status_Header">
            <Properties>
              <Left>7700</Left>
              <Top>200</Top>
              <Width>800</Width>
              <Height>400</Height>
              <Caption>الحالة</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_RegistrationDate_Header">
            <Properties>
              <Left>8500</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>تاريخ التسجيل</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
          
          <Label name="lbl_ParentPhone_Header">
            <Properties>
              <Left>9500</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>هاتف ولي الأمر</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
        </Controls>
      </PageHeader>
      
      <!-- Detail Section -->
      <Detail>
        <Height>500</Height>
        <Controls>
          <TextBox name="txt_StudentID">
            <Properties>
              <Left>200</Left>
              <Top>50</Top>
              <Width>600</Width>
              <Height>400</Height>
              <ControlSource>StudentID</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_StudentName">
            <Properties>
              <Left>800</Left>
              <Top>50</Top>
              <Width>1500</Width>
              <Height>400</Height>
              <ControlSource>StudentName</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_CourseName">
            <Properties>
              <Left>2300</Left>
              <Top>50</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <ControlSource>CourseName</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_TeacherName">
            <Properties>
              <Left>3500</Left>
              <Top>50</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <ControlSource>TeacherName</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_TotalFee">
            <Properties>
              <Left>4700</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>TotalFee</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_PaidAmount">
            <Properties>
              <Left>5700</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>PaidAmount</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
              <ForeColor>#27AE60</ForeColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_RemainingAmount">
            <Properties>
              <Left>6700</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>RemainingAmount</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
              <ForeColor>=IIf([RemainingAmount]>0,"#E74C3C","#27AE60")</ForeColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_StudentStatus">
            <Properties>
              <Left>7700</Left>
              <Top>50</Top>
              <Width>800</Width>
              <Height>400</Height>
              <ControlSource>StudentStatus</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
              <ForeColor>=IIf([StudentStatus]="Active","#27AE60","#E74C3C")</ForeColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_RegistrationDate">
            <Properties>
              <Left>8500</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>RegistrationDate</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <Format>Short Date</Format>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <TextBox name="txt_ParentPhone">
            <Properties>
              <Left>9500</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>ParentPhone</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
        </Controls>
      </Detail>
      
      <!-- Report Footer -->
      <ReportFooter>
        <Height>1200</Height>
        <BackColor>#ECF0F1</BackColor>
        
        <Controls>
          <Label name="lbl_TotalStudents">
            <Properties>
              <Left>500</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>300</Height>
              <Caption>إجمالي عدد الطلاب:</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Right</TextAlign>
            </Properties>
          </Label>
          
          <TextBox name="txt_TotalStudentsCount">
            <Properties>
              <Left>2000</Left>
              <Top>200</Top>
              <Width>800</Width>
              <Height>300</Height>
              <ControlSource>=Count([StudentID])</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BackColor>#FFFFFF</BackColor>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <Label name="lbl_TotalFees">
            <Properties>
              <Left>3000</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>300</Height>
              <Caption>إجمالي الأقساط:</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Right</TextAlign>
            </Properties>
          </Label>
          
          <TextBox name="txt_TotalFeesSum">
            <Properties>
              <Left>4500</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>300</Height>
              <ControlSource>=Sum([TotalFee])</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BackColor>#FFFFFF</BackColor>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
          
          <Label name="lbl_TotalPaid">
            <Properties>
              <Left>6000</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>300</Height>
              <Caption>إجمالي المدفوع:</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Right</TextAlign>
            </Properties>
          </Label>
          
          <TextBox name="txt_TotalPaidSum">
            <Properties>
              <Left>7500</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>300</Height>
              <ControlSource>=Sum([PaidAmount])</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BackColor>#FFFFFF</BackColor>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
              <ForeColor>#27AE60</ForeColor>
            </Properties>
          </TextBox>
          
          <Label name="lbl_TotalRemaining">
            <Properties>
              <Left>500</Left>
              <Top>600</Top>
              <Width>1500</Width>
              <Height>300</Height>
              <Caption>إجمالي المتبقي:</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Right</TextAlign>
            </Properties>
          </Label>
          
          <TextBox name="txt_TotalRemainingSum">
            <Properties>
              <Left>2000</Left>
              <Top>600</Top>
              <Width>1200</Width>
              <Height>300</Height>
              <ControlSource>=Sum([RemainingAmount])</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BackColor>#FFFFFF</BackColor>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
              <ForeColor>#E74C3C</ForeColor>
            </Properties>
          </TextBox>
          
          <Label name="lbl_ReportGenerated">
            <Properties>
              <Left>6000</Left>
              <Top>600</Top>
              <Width>3000</Width>
              <Height>300</Height>
              <Caption>=Format(Now(),"dd/mm/yyyy hh:nn AM/PM") & " تم إنتاج التقرير في"</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Left</TextAlign>
              <ForeColor>#7F8C8D</ForeColor>
            </Properties>
          </Label>
        </Controls>
      </ReportFooter>
      
      <!-- Page Footer -->
      <PageFooter>
        <Height>400</Height>
        <Controls>
          <Label name="lbl_PageNumber">
            <Properties>
              <Left>5000</Left>
              <Top>100</Top>
              <Width>1000</Width>
              <Height>200</Height>
              <Caption>="صفحة " & [Page] & " من " & [Pages]</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
            </Properties>
          </Label>
        </Controls>
      </PageFooter>
    </Sections>
  </Report>

  <!-- =============================================== -->
  <!-- تقرير الدفعات الشهري -->
  <!-- =============================================== -->
  <Report name="MonthlyPaymentsReport">
    <Properties>
      <Caption>تقرير الدفعات الشهري</Caption>
      <RecordSource>
        SELECT P.PaymentID, P.PaymentDate, S.StudentName, P.AmountPaid,
               P.PaymentMethod, P.ReceiptNumber, T.TeacherName, C.CourseName
        FROM ((Payments P
        INNER JOIN Students S ON P.StudentID = S.StudentID)
        LEFT JOIN Teachers T ON S.TeacherID = T.TeacherID)
        LEFT JOIN Courses C ON S.CourseID = C.CourseID
        WHERE Month(P.PaymentDate) = Month(Date()) AND Year(P.PaymentDate) = Year(Date())
        ORDER BY P.PaymentDate DESC
      </RecordSource>
      <Width>11000</Width>
      <Height>8000</Height>
      <RightToLeft>True</RightToLeft>
    </Properties>

    <Sections>
      <ReportHeader>
        <Height>1500</Height>
        <BackColor>#27AE60</BackColor>

        <Controls>
          <Label name="lbl_PaymentsReportTitle">
            <Properties>
              <Left>500</Left>
              <Top>300</Top>
              <Width>10000</Width>
              <Height>600</Height>
              <Caption>تقرير الدفعات الشهري</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>24</FontSize>
              <FontWeight>Bold</FontWeight>
              <ForeColor>#FFFFFF</ForeColor>
              <BackStyle>Transparent</BackStyle>
              <TextAlign>Center</TextAlign>
            </Properties>
          </Label>

          <Label name="lbl_PaymentsMonth">
            <Properties>
              <Left>500</Left>
              <Top>900</Top>
              <Width>10000</Width>
              <Height>400</Height>
              <Caption>=Format(Date(),"mmmm yyyy")</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>16</FontSize>
              <ForeColor>#FFFFFF</ForeColor>
              <BackStyle>Transparent</BackStyle>
              <TextAlign>Center</TextAlign>
            </Properties>
          </Label>
        </Controls>
      </ReportHeader>

      <PageHeader>
        <Height>800</Height>
        <BackColor>#ECF0F1</BackColor>

        <Controls>
          <Label name="lbl_PaymentDate_Header">
            <Properties>
              <Left>200</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>تاريخ الدفع</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>

          <Label name="lbl_StudentName_PayHeader">
            <Properties>
              <Left>1200</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>400</Height>
              <Caption>اسم الطالب</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>

          <Label name="lbl_AmountPaid_Header">
            <Properties>
              <Left>2700</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <Caption>المبلغ المدفوع</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>

          <Label name="lbl_PaymentMethod_Header">
            <Properties>
              <Left>3900</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>طريقة الدفع</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>

          <Label name="lbl_ReceiptNumber_Header">
            <Properties>
              <Left>4900</Left>
              <Top>200</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <Caption>رقم الإيصال</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>

          <Label name="lbl_TeacherName_PayHeader">
            <Properties>
              <Left>5900</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <Caption>المدرس</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>

          <Label name="lbl_CourseName_PayHeader">
            <Properties>
              <Left>7100</Left>
              <Top>200</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <Caption>الدورة</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>12</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </Label>
        </Controls>
      </PageHeader>

      <Detail>
        <Height>500</Height>
        <Controls>
          <TextBox name="txt_PaymentDate">
            <Properties>
              <Left>200</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>PaymentDate</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <Format>Short Date</Format>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>

          <TextBox name="txt_StudentName_Pay">
            <Properties>
              <Left>1200</Left>
              <Top>50</Top>
              <Width>1500</Width>
              <Height>400</Height>
              <ControlSource>StudentName</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>

          <TextBox name="txt_AmountPaid">
            <Properties>
              <Left>2700</Left>
              <Top>50</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <ControlSource>AmountPaid</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
              <ForeColor>#27AE60</ForeColor>
              <FontWeight>Bold</FontWeight>
            </Properties>
          </TextBox>

          <TextBox name="txt_PaymentMethod">
            <Properties>
              <Left>3900</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>PaymentMethod</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>

          <TextBox name="txt_ReceiptNumber">
            <Properties>
              <Left>4900</Left>
              <Top>50</Top>
              <Width>1000</Width>
              <Height>400</Height>
              <ControlSource>ReceiptNumber</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>

          <TextBox name="txt_TeacherName_Pay">
            <Properties>
              <Left>5900</Left>
              <Top>50</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <ControlSource>TeacherName</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>

          <TextBox name="txt_CourseName_Pay">
            <Properties>
              <Left>7100</Left>
              <Top>50</Top>
              <Width>1200</Width>
              <Height>400</Height>
              <ControlSource>CourseName</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#BDC3C7</BorderColor>
            </Properties>
          </TextBox>
        </Controls>
      </Detail>

      <ReportFooter>
        <Height>1000</Height>
        <BackColor>#ECF0F1</BackColor>

        <Controls>
          <Label name="lbl_TotalPayments">
            <Properties>
              <Left>500</Left>
              <Top>200</Top>
              <Width>2000</Width>
              <Height>300</Height>
              <Caption>إجمالي الدفعات للشهر:</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>14</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Right</TextAlign>
            </Properties>
          </Label>

          <TextBox name="txt_TotalPaymentsSum">
            <Properties>
              <Left>2500</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>300</Height>
              <ControlSource>=Sum([AmountPaid])</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>14</FontSize>
              <FontWeight>Bold</FontWeight>
              <Format>Currency</Format>
              <TextAlign>Center</TextAlign>
              <BackColor>#FFFFFF</BackColor>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#27AE60</BorderColor>
              <BorderWidth>2</BorderWidth>
              <ForeColor>#27AE60</ForeColor>
            </Properties>
          </TextBox>

          <Label name="lbl_PaymentCount">
            <Properties>
              <Left>4500</Left>
              <Top>200</Top>
              <Width>1500</Width>
              <Height>300</Height>
              <Caption>عدد الدفعات:</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>14</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Right</TextAlign>
            </Properties>
          </Label>

          <TextBox name="txt_PaymentCountSum">
            <Properties>
              <Left>6000</Left>
              <Top>200</Top>
              <Width>800</Width>
              <Height>300</Height>
              <ControlSource>=Count([PaymentID])</ControlSource>
              <FontName>Tahoma</FontName>
              <FontSize>14</FontSize>
              <FontWeight>Bold</FontWeight>
              <TextAlign>Center</TextAlign>
              <BackColor>#FFFFFF</BackColor>
              <BorderStyle>Solid</BorderStyle>
              <BorderColor>#3498DB</BorderColor>
              <BorderWidth>2</BorderWidth>
              <ForeColor>#3498DB</ForeColor>
            </Properties>
          </TextBox>

          <Label name="lbl_PaymentReportGenerated">
            <Properties>
              <Left>500</Left>
              <Top>600</Top>
              <Width>4000</Width>
              <Height>300</Height>
              <Caption>=Format(Now(),"dd/mm/yyyy hh:nn AM/PM") & " تم إنتاج التقرير في"</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Right</TextAlign>
              <ForeColor>#7F8C8D</ForeColor>
            </Properties>
          </Label>
        </Controls>
      </ReportFooter>

      <PageFooter>
        <Height>400</Height>
        <Controls>
          <Label name="lbl_PaymentPageNumber">
            <Properties>
              <Left>5000</Left>
              <Top>100</Top>
              <Width>1000</Width>
              <Height>200</Height>
              <Caption>="صفحة " & [Page] & " من " & [Pages]</Caption>
              <FontName>Tahoma</FontName>
              <FontSize>10</FontSize>
              <TextAlign>Center</TextAlign>
            </Properties>
          </Label>
        </Controls>
      </PageFooter>
    </Sections>
  </Report>
</AccessReportsDesign>
