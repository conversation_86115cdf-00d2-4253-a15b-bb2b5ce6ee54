<?xml version="1.0" encoding="UTF-8"?>
<!-- نظام محاسبة المعاهد الأهلية العراقية - تصميم النماذج -->
<AccessFormsDesign>
    
    <!-- =============================================== -->
    <!-- نموذج الواجهة الرئيسية -->
    <!-- =============================================== -->
    <MainForm name="الواجهة_الرئيسية">
        <Properties>
            <Caption>نظام محاسبة المعاهد الأهلية العراقية</Caption>
            <BackColor>#F0F0F0</BackColor>
            <BorderStyle>Dialog</BorderStyle>
            <ControlBox>True</ControlBox>
            <MinMaxButtons>Both</MinMaxButtons>
            <Width>1200</Width>
            <Height>800</Height>
            <PopUp>False</PopUp>
            <Modal>False</Modal>
            <RightToLeft>True</RightToLeft>
        </Properties>
        
        <Controls>
            <!-- الشعار واسم النظام -->
            <Label name="lbl_عنوان_النظام">
                <Properties>
                    <Left>400</Left>
                    <Top>20</Top>
                    <Width>400</Width>
                    <Height>60</Height>
                    <Caption>نظام محاسبة المعاهد الأهلية العراقية</Caption>
                    <FontSize>24</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <ForeColor>#2C3E50</ForeColor>
                </Properties>
            </Label>
            
            <!-- لوحة الإحصائيات -->
            <Rectangle name="rect_إحصائيات">
                <Properties>
                    <Left>50</Left>
                    <Top>100</Top>
                    <Width>1100</Width>
                    <Height>200</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Shadowed</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <!-- إحصائيات الطلاب -->
            <Rectangle name="rect_طلاب">
                <Properties>
                    <Left>70</Left>
                    <Top>120</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <BackColor>#E8F4FD</BackColor>
                    <BorderColor>#3498DB</BorderColor>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_عدد_الطلاب">
                <Properties>
                    <Left>80</Left>
                    <Top>130</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <Caption>عدد الطلاب النشطين</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Center</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_عدد_الطلاب">
                <Properties>
                    <Left>80</Left>
                    <Top>160</Top>
                    <Width>180</Width>
                    <Height>30</Height>
                    <FontSize>16</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <BackColor>#FFFFFF</BackColor>
                    <Locked>True</Locked>
                    <ControlSource>=عدد_الطلاب_النشطين()</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- إحصائيات المدرسين -->
            <Rectangle name="rect_مدرسين">
                <Properties>
                    <Left>290</Left>
                    <Top>120</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <BackColor>#E8F6F0</BackColor>
                    <BorderColor>#27AE60</BorderColor>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_عدد_المدرسين">
                <Properties>
                    <Left>300</Left>
                    <Top>130</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <Caption>عدد المدرسين النشطين</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Center</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_عدد_المدرسين">
                <Properties>
                    <Left>300</Left>
                    <Top>160</Top>
                    <Width>180</Width>
                    <Height>30</Height>
                    <FontSize>16</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <BackColor>#FFFFFF</BackColor>
                    <Locked>True</Locked>
                    <ControlSource>=عدد_المدرسين_النشطين()</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- إحصائيات الأرباح -->
            <Rectangle name="rect_أرباح">
                <Properties>
                    <Left>510</Left>
                    <Top>120</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <BackColor>#FDF2E9</BackColor>
                    <BorderColor>#F39C12</BorderColor>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_الأرباح_الشهرية">
                <Properties>
                    <Left>520</Left>
                    <Top>130</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <Caption>الأرباح الصافية الشهرية</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Center</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_الأرباح_الشهرية">
                <Properties>
                    <Left>520</Left>
                    <Top>160</Top>
                    <Width>180</Width>
                    <Height>30</Height>
                    <FontSize>16</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <BackColor>#FFFFFF</BackColor>
                    <Locked>True</Locked>
                    <ControlSource>=الأرباح_الصافية_الشهرية(Month(Now()), Year(Now()))</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- إحصائيات الأقساط غير المدفوعة -->
            <Rectangle name="rect_أقساط_غير_مدفوعة">
                <Properties>
                    <Left>730</Left>
                    <Top>120</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <BackColor>#FDEDEC</BackColor>
                    <BorderColor>#E74C3C</BorderColor>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_أقساط_غير_مدفوعة">
                <Properties>
                    <Left>740</Left>
                    <Top>130</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <Caption>الأقساط غير المدفوعة</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Center</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_أقساط_غير_مدفوعة">
                <Properties>
                    <Left>740</Left>
                    <Top>160</Top>
                    <Width>180</Width>
                    <Height>30</Height>
                    <FontSize>16</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <BackColor>#FFFFFF</BackColor>
                    <Locked>True</Locked>
                    <ControlSource>=إجمالي_الأقساط_غير_المدفوعة()</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- أزرار التنقل الرئيسية -->
            <Button name="btn_إدارة_الطلاب">
                <Properties>
                    <Left>100</Left>
                    <Top>350</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>إدارة الطلاب</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#3498DB</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#2980B9</HoverColor>
                    <OnClick>DoCmd.OpenForm "نموذج_الطلاب"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_إدارة_المدرسين">
                <Properties>
                    <Left>350</Left>
                    <Top>350</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>إدارة المدرسين</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#27AE60</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#229954</HoverColor>
                    <OnClick>DoCmd.OpenForm "نموذج_المدرسين"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_الحسابات">
                <Properties>
                    <Left>600</Left>
                    <Top>350</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>الحسابات والمصاريف</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#F39C12</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#E67E22</HoverColor>
                    <OnClick>DoCmd.OpenForm "نموذج_الحسابات"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_التقارير">
                <Properties>
                    <Left>850</Left>
                    <Top>350</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>التقارير</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#8E44AD</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#7D3C98</HoverColor>
                    <OnClick>DoCmd.OpenForm "نموذج_التقارير"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_الإعدادات">
                <Properties>
                    <Left>225</Left>
                    <Top>460</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>إعدادات المعهد</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#34495E</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#2C3E50</HoverColor>
                    <OnClick>DoCmd.OpenForm "نموذج_الإعدادات"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_الدورات">
                <Properties>
                    <Left>475</Left>
                    <Top>460</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>إدارة الدورات</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#E67E22</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#D35400</HoverColor>
                    <OnClick>DoCmd.OpenForm "نموذج_الدورات"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_النسخ_الاحتياطي">
                <Properties>
                    <Left>725</Left>
                    <Top>460</Top>
                    <Width>200</Width>
                    <Height>80</Height>
                    <Caption>النسخ الاحتياطي</Caption>
                    <FontSize>14</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <BackColor>#95A5A6</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <HoverColor>#7F8C8D</HoverColor>
                    <OnClick>[Event Procedure]</OnClick>
                </Properties>
            </Button>
            
            <!-- معلومات النظام -->
            <Label name="lbl_معلومات_النظام">
                <Properties>
                    <Left>50</Left>
                    <Top>700</Top>
                    <Width>1100</Width>
                    <Height>30</Height>
                    <Caption>نظام محاسبة المعاهد الأهلية العراقية - الإصدار 1.0 - تطوير Zencoder AI</Caption>
                    <FontSize>10</FontSize>
                    <TextAlign>Center</TextAlign>
                    <ForeColor>#7F8C8D</ForeColor>
                </Properties>
            </Label>
        </Controls>
    </MainForm>
    
    <!-- =============================================== -->
    <!-- نموذج إدارة الطلاب -->
    <!-- =============================================== -->
    <StudentsForm name="نموذج_الطلاب">
        <Properties>
            <Caption>إدارة الطلاب</Caption>
            <BackColor>#F8F9FA</BackColor>
            <Width>1400</Width>
            <Height>900</Height>
            <RightToLeft>True</RightToLeft>
            <RecordSource>الطلاب</RecordSource>
        </Properties>
        
        <Controls>
            <!-- عنوان النموذج -->
            <Label name="lbl_عنوان_الطلاب">
                <Properties>
                    <Left>600</Left>
                    <Top>20</Top>
                    <Width>200</Width>
                    <Height>40</Height>
                    <Caption>إدارة الطلاب</Caption>
                    <FontSize>18</FontSize>
                    <FontWeight>Bold</FontWeight>
                    <TextAlign>Center</TextAlign>
                    <ForeColor>#2C3E50</ForeColor>
                </Properties>
            </Label>
            
            <!-- بيانات الطالب -->
            <Rectangle name="rect_بيانات_الطالب">
                <Properties>
                    <Left>50</Left>
                    <Top>80</Top>
                    <Width>1300</Width>
                    <Height>300</Height>
                    <BackColor>#FFFFFF</BackColor>
                    <BorderColor>#BDC3C7</BorderColor>
                    <SpecialEffect>Sunken</SpecialEffect>
                </Properties>
            </Rectangle>
            
            <Label name="lbl_اسم_الطالب">
                <Properties>
                    <Left>1200</Left>
                    <Top>100</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>اسم الطالب:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_اسم_الطالب">
                <Properties>
                    <Left>900</Left>
                    <Top>100</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>اسم_الطالب</ControlSource>
                </Properties>
            </Textbox>
            
            <Label name="lbl_الجنس">
                <Properties>
                    <Left>800</Left>
                    <Top>100</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>الجنس:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_الجنس">
                <Properties>
                    <Left>650</Left>
                    <Top>100</Top>
                    <Width>120</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>الجنس</ControlSource>
                    <RowSource>"ذكر";"أنثى"</RowSource>
                </Properties>
            </ComboBox>
            
            <Label name="lbl_العمر">
                <Properties>
                    <Left>580</Left>
                    <Top>100</Top>
                    <Width>60</Width>
                    <Height>25</Height>
                    <Caption>العمر:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_العمر">
                <Properties>
                    <Left>480</Left>
                    <Top>100</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>العمر</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- صف دراسي ورقم هاتف -->
            <Label name="lbl_الصف_الدراسي">
                <Properties>
                    <Left>1200</Left>
                    <Top>140</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>الصف الدراسي:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_الصف_الدراسي">
                <Properties>
                    <Left>900</Left>
                    <Top>140</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>الصف_الدراسي</ControlSource>
                </Properties>
            </Textbox>
            
            <Label name="lbl_رقم_الهاتف">
                <Properties>
                    <Left>800</Left>
                    <Top>140</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>رقم الهاتف:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_رقم_الهاتف">
                <Properties>
                    <Left>550</Left>
                    <Top>140</Top>
                    <Width>220</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>رقم_الهاتف</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- هاتف ولي الأمر -->
            <Label name="lbl_هاتف_ولي_الامر">
                <Properties>
                    <Left>1200</Left>
                    <Top>180</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>هاتف ولي الأمر:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_هاتف_ولي_الامر">
                <Properties>
                    <Left>900</Left>
                    <Top>180</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>هاتف_ولي_الامر</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- العنوان -->
            <Label name="lbl_العنوان">
                <Properties>
                    <Left>800</Left>
                    <Top>180</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>العنوان:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_العنوان">
                <Properties>
                    <Left>400</Left>
                    <Top>180</Top>
                    <Width>370</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>العنوان</ControlSource>
                </Properties>
            </Textbox>
            
            <!-- الدورة والمدرس -->
            <Label name="lbl_الدورة">
                <Properties>
                    <Left>1200</Left>
                    <Top>220</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>الدورة:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_الدورة">
                <Properties>
                    <Left>900</Left>
                    <Top>220</Top>
                    <Width>280</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>معرف_الدورة</ControlSource>
                    <RowSource>SELECT معرف_الدورة, اسم_الدورة FROM الدورات WHERE حالة_الدورة = 'نشطة'</RowSource>
                    <BoundColumn>1</BoundColumn>
                    <ColumnCount>2</ColumnCount>
                </Properties>
            </ComboBox>
            
            <Label name="lbl_المدرس">
                <Properties>
                    <Left>800</Left>
                    <Top>220</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>المدرس:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <ComboBox name="cmb_المدرس">
                <Properties>
                    <Left>520</Left>
                    <Top>220</Top>
                    <Width>250</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>معرف_المدرس</ControlSource>
                    <RowSource>SELECT معرف_المدرس, اسم_المدرس FROM المدرسين WHERE حالة_المدرس = 'نشط'</RowSource>
                    <BoundColumn>1</BoundColumn>
                    <ColumnCount>2</ColumnCount>
                </Properties>
            </ComboBox>
            
            <!-- الرسوم والمبالغ -->
            <Label name="lbl_رسوم_الدورة">
                <Properties>
                    <Left>1200</Left>
                    <Top>260</Top>
                    <Width>100</Width>
                    <Height>25</Height>
                    <Caption>رسوم الدورة:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_رسوم_الدورة">
                <Properties>
                    <Left>1000</Left>
                    <Top>260</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>رسوم_الدورة</ControlSource>
                    <Format>Currency</Format>
                </Properties>
            </Textbox>
            
            <Label name="lbl_المبلغ_المدفوع">
                <Properties>
                    <Left>900</Left>
                    <Top>260</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>المبلغ المدفوع:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_المبلغ_المدفوع">
                <Properties>
                    <Left>700</Left>
                    <Top>260</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>المبلغ_المدفوع</ControlSource>
                    <Format>Currency</Format>
                    <Locked>True</Locked>
                </Properties>
            </Textbox>
            
            <Label name="lbl_المبلغ_المتبقي">
                <Properties>
                    <Left>600</Left>
                    <Top>260</Top>
                    <Width>80</Width>
                    <Height>25</Height>
                    <Caption>المبلغ المتبقي:</Caption>
                    <FontSize>12</FontSize>
                    <TextAlign>Right</TextAlign>
                </Properties>
            </Label>
            
            <Textbox name="txt_المبلغ_المتبقي">
                <Properties>
                    <Left>400</Left>
                    <Top>260</Top>
                    <Width>180</Width>
                    <Height>25</Height>
                    <FontSize>12</FontSize>
                    <ControlSource>المبلغ_المتبقي</ControlSource>
                    <Format>Currency</Format>
                    <Locked>True</Locked>
                </Properties>
            </Textbox>
            
            <!-- أزرار العمليات -->
            <Button name="btn_طالب_جديد">
                <Properties>
                    <Left>1200</Left>
                    <Top>320</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>طالب جديد</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#27AE60</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.GoToRecord , , acNewRec</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_حفظ_الطالب">
                <Properties>
                    <Left>1070</Left>
                    <Top>320</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>حفظ البيانات</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#3498DB</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.RunCommand acCmdSaveRecord</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_حذف_الطالب">
                <Properties>
                    <Left>940</Left>
                    <Top>320</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>حذف الطالب</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#E74C3C</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.RunCommand acCmdDeleteRecord</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_انسحاب_الطالب">
                <Properties>
                    <Left>810</Left>
                    <Top>320</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>انسحاب الطالب</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#F39C12</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.OpenForm "نموذج_انسحاب_الطالب"</OnClick>
                </Properties>
            </Button>
            
            <Button name="btn_دفع_قسط">
                <Properties>
                    <Left>680</Left>
                    <Top>320</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>دفع قسط</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#8E44AD</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.OpenForm "نموذج_دفع_قسط"</OnClick>
                </Properties>
            </Button>
            
            <!-- قائمة الطلاب -->
            <SubForm name="subform_قائمة_الطلاب">
                <Properties>
                    <Left>50</Left>
                    <Top>400</Top>
                    <Width>1300</Width>
                    <Height>400</Height>
                    <SourceObject>Query.استعلام_الطلاب</SourceObject>
                    <LinkChildFields>معرف_الطالب</LinkChildFields>
                    <LinkMasterFields>معرف_الطالب</LinkMasterFields>
                </Properties>
            </SubForm>
            
            <!-- زر العودة -->
            <Button name="btn_العودة">
                <Properties>
                    <Left>100</Left>
                    <Top>820</Top>
                    <Width>120</Width>
                    <Height>35</Height>
                    <Caption>العودة للرئيسية</Caption>
                    <FontSize>12</FontSize>
                    <BackColor>#95A5A6</BackColor>
                    <ForeColor>#FFFFFF</ForeColor>
                    <OnClick>DoCmd.OpenForm "الواجهة_الرئيسية"</OnClick>
                </Properties>
            </Button>
        </Controls>
    </StudentsForm>
</AccessFormsDesign>